# 使用更明确的基础镜像 TAG
FROM hub.kubesphere.com.cn/aicp/python:3.11.7-slim-bullseye

ARG TARGETARCH

# 配置阿里云镜像源
RUN sed -i 's/deb.debian.org/mirrors.tuna.tsinghua.edu.cn/g' /etc/apt/sources.list

WORKDIR /code

RUN apt update && \
    apt install -y --no-install-recommends python3-dev gcc openssh-client vim\
    && apt clean && \
    rm -rf /var/lib/apt/lists/*

COPY ./requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r requirements.txt \
    -i https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com

COPY . .

ENV PYTHONPATH=/code

#ENTRYPOINT ["gunicorn", "app.main:application", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:5000"]
ENTRYPOINT ["uvicorn","app.main:application" ,"--host" ,"0.0.0.0","--port" ,"5000" ,"--workers" ,"4"]