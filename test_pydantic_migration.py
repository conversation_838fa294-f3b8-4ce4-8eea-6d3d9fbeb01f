#!/usr/bin/env python3
"""
Test script to verify Pydantic V1 to V2 migration
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all models can be imported without errors"""
    print("Testing model imports...")
    
    try:
        # Test core models
        from app.core.models import ActiveRecordMixin, BaseModel
        print("✓ Core models imported successfully")
    except Exception as e:
        print(f"✗ Error importing core models: {e}")
        return False
    
    try:
        # Test notebook models
        from app.models.notebooks import NotebookRead, NotebookCreate
        print("✓ Notebook models imported successfully")
    except Exception as e:
        print(f"✗ Error importing notebook models: {e}")
        return False
    
    try:
        # Test train models
        from app.models.trains import TrainRead, TrainCreate
        print("✓ Train models imported successfully")
    except Exception as e:
        print(f"✗ Error importing train models: {e}")
        return False
    
    try:
        # Test resource group models
        from app.models.resource_group import RepResourceGroupAddNode, AddResourceGroupShareUser
        print("✓ Resource group models imported successfully")
    except Exception as e:
        print(f"✗ Error importing resource group models: {e}")
        return False
    
    try:
        # Test data set models
        from app.models.data_set import DataSetResponse
        print("✓ Data set models imported successfully")
    except Exception as e:
        print(f"✗ Error importing data set models: {e}")
        return False
    
    return True

def test_pydantic_v2_features():
    """Test that Pydantic V2 features work correctly"""
    print("\nTesting Pydantic V2 features...")
    
    try:
        from pydantic import BaseModel, Field, ConfigDict
        
        class TestModel(BaseModel):
            name: str = Field(..., description="Test name")
            items: list[str] = Field(default=[], description="Test items")
            
            model_config = ConfigDict(extra='allow')
        
        # Test model_dump (V2 method)
        test_instance = TestModel(name="test", items=["a", "b"])
        data = test_instance.model_dump()
        assert data == {"name": "test", "items": ["a", "b"]}
        print("✓ model_dump() works correctly")
        
        # Test model_validate (V2 method)
        validated = TestModel.model_validate({"name": "test2", "items": ["c", "d"]})
        assert validated.name == "test2"
        print("✓ model_validate() works correctly")
        
        return True
    except Exception as e:
        print(f"✗ Error testing Pydantic V2 features: {e}")
        return False

def main():
    """Main test function"""
    print("Pydantic V1 to V2 Migration Test")
    print("=" * 40)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test Pydantic V2 features
    if not test_pydantic_v2_features():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✓ All tests passed! Pydantic V2 migration successful!")
        return 0
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
