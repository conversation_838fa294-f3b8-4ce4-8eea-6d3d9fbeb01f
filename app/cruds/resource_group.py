from datetime import datetime
from typing import Any, List, Optional, Sequence

from kubernetes.client import V1Taint
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, delete, desc
from sqlmodel import asc, select, or_
from sqlmodel import Session
import app
from app import logger
from app.core.qingcloud.resource import ProductCenterResource
from app.models.gpu import GpuFaultRecords, GpuUnschedulableXids
from app.models.notebooks import NotebookReplicaSpec, Notebook
from app.apps.resource_group.exceptions import ResourceGroupNodeNotFoundException, ResourceGroupNotFoundException, \
    ResourceGroupNodeSuspended
from app.models.operation_record import OperationRecord
from app.models.resource_group import ResourceGroupCreate, ResourceGroup, ResourceGroupRep, ResourceNode, \
    ResourceGroupUpdate, \
    ResourceNodeStatus, ResourceGroupShare, CreateResourceTemplateReq, ResourceTemplate, ResourceTemplateQuery, \
    TemplateSpecType, UpdateResourceTemplateReq, \
    pod_type_emu
from app.common.response import PERMISSION_DENIED, RESOURCE_IS_RUNNING
from app.core.constant import RG_NODE_TOLERATION_KEY, RG_NODE_TOLERATION_KEY_SUSPENDED, STATUS_SUSPENDED, \
    USER_ROLE_ADMIN, STATUS_ACTIVE, NODE_TAG_LOCK, \
    RG_TAG_KEY, \
    RG_NODE_TAG_KEY, \
    STATUS_TERMINATED, RG_TOLERATION_KEY
from app.core.exceptions import PermissionDeniedException, ParameterException
from app.core.kube.api import read_nodes, replace_node_tag, check_user_resource
from app.core.middlewares.auth.qingcloud_auth import QingcloudUser
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.interface import describe_user_without_redis, describe_sub_users
from app.core.response import BaseGenericResponse
from app.core.rlock import Rlock
from app.core.utils import generate_random_string, log_background_task_exception


class ResourceGroupCrud:
    """
    Train CRUD
    """

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session: Session = session
        self.user = user

    def generate_rg_id(self, exclude_ids=None):
        while True:
            rg_id = "rg-" + generate_random_string()
            logger.info("get rg_id [%s]", rg_id)
            resource_group = self.get_rg(rg_id)
            if resource_group:
                continue
            if exclude_ids and rg_id in exclude_ids:
                continue
            return rg_id

    def generate_rgn_id(self, exclude_ids=None):
        while True:
            rg_node_id = "rgn-" + generate_random_string()
            logger.info("get rg_id [%s]", rg_node_id)
            resource_group = self.get_rgn(rg_node_id)
            if resource_group:
                continue
            if exclude_ids and rg_node_id in exclude_ids:
                continue
            return rg_node_id

    def create_rg(self, resource_group_create: ResourceGroupCreate, user: QingcloudUser) -> Any:
        resource_group = ResourceGroup(**resource_group_create.dict())
        resource_group.rg_id = self.generate_rg_id()
        resource_group.user_id = user.user_id
        resource_group.root_user_id = user.root_user_id
        resource_group.status = STATUS_ACTIVE
        resource_group.customize_enable = resource_group_create.customize_enable
        pod_type_list = [p if isinstance(p, str) else p.value for p in resource_group_create.pod_type]
        if pod_type_emu.issuperset(set(pod_type_list)):
            pod_type = ','.join(sorted(pod_type_list))
        else:
            raise ParameterException(resource_group_create.pod_type)
        resource_group.pod_type = pod_type
        self.session.add(resource_group)
        self.session.commit()
        self.session.refresh(resource_group)
        return resource_group

    def update_rg(self, update_rg: ResourceGroupUpdate, user: QingcloudUser) -> Any:
        if user.role != USER_ROLE_ADMIN:
            resource_group = self.get_rg_by_user(user)
            if not resource_group:
                return None
        results = self.session.exec(select(
            ResourceGroup).where(ResourceGroup.rg_id == update_rg.rg_id,
                                 ResourceGroup.user_id == user.user_id))
        rg = results.one()
        rg.name = update_rg.name
        rg.description = update_rg.description
        rg.customize_enable = update_rg.customize_enable
        pod_type_list = [p if isinstance(p, str) else p.value for p in update_rg.pod_type]
        if pod_type_emu.issuperset(set(pod_type_list)):
            pod_type = ','.join(sorted(pod_type_list))
        else:
            raise ParameterException(update_rg.pod_type)
        rg.pod_type = pod_type
        if rg:
            self.session.commit()
        return rg

    # def remove_rg(self, rg_id: str, user: QingcloudUser) -> Any:
    #     '''
    #     only update status do not delete from db
    #     :param rg_id:
    #     :param user:
    #     :return:
    #     '''
    #     if user.role != USER_ROLE_ADMIN:
    #         resource_group = self.get_rg_by_user(user.user_id)
    #         if not resource_group:
    #             return None
    #     sql = (update(ResourceGroup).where(
    #         ResourceGroup.rg_id == rg_id).values(
    #         ResourceGroup.status == STATUS_DETELE))
    #     r = self.session.exec(sql)
    #     self.session.commit()
    #     return r

    def delete_rg(self, rg_id: str, user: QingcloudUser) -> Any:
        if user.role != USER_ROLE_ADMIN:
            results = self.session.exec(select(
                ResourceGroup).where(ResourceGroup.rg_id == rg_id,
                                     ResourceGroup.user_id == user.user_id))
        else:
            results = self.session.exec(select(
                ResourceGroup).where(ResourceGroup.rg_id == rg_id))
        rg = results.one_or_none()
        if not rg:
            logger.warning("no fount rg[%s], user[%s]", rg_id, user.user_id)
            return False
        rg.status = STATUS_TERMINATED
        if self.get_rgs(rg_id):
            if not self.delete_rgn_by_rg_id(rg_id):
                self.session.rollback()
                return None
        self.session.commit()
        self.session.refresh(rg)
        return True

    def delete_rgn_by_rg_id(self, rg_id: str) -> Any:
        results = self.session.exec(
            select(ResourceNode).where(ResourceNode.rg_id == rg_id))
        resource_nodes = results.all()
        for rgn in resource_nodes:
            if rgn.billing_order_id:
                continue
            rgn.status = STATUS_TERMINATED
            node = read_nodes(rgn.hostname)
            node_labels = node.metadata.labels
            logger.info("get node_labels [%s]", node_labels)
            if node_labels[RG_TAG_KEY] == rgn.rg_id:
                del node_labels[RG_TAG_KEY]
            if RG_NODE_TAG_KEY in node_labels:
                del node_labels[RG_NODE_TAG_KEY]
            taints = node.spec.taints
            for taint in taints:
                if taint.key in [RG_TOLERATION_KEY, RG_NODE_TOLERATION_KEY]:
                    node.spec.taints.remove(taint)
                    break
            with Rlock(NODE_TAG_LOCK):
                if not replace_node_tag(rgn.hostname, node):
                    logger.critical("remove node tag from k8s failed")
                    self.session.rollback()
                    return None
        self.session.commit()
        return True

    def delete_rgn_by_rg_node_ids(self, rg_node_ids: list, user_id=None):
        for rg_node_id in rg_node_ids:
            results = self.session.exec(
                select(ResourceNode).where(ResourceNode.rg_node_id == rg_node_id))
            resource_node: ResourceNode = results.one()
            if self.user.is_kse_admin_user():
                resource_user_id = resource_node.user_id
            else:
                resource_user_id = user_id
            if resource_node.billing_order_id:
                if app.settings.billing_enable:
                    billing = QAIBillingService()
                    billing.unlease(rg_node_id, resource_user_id)
            resource_node.status = STATUS_TERMINATED
            node = read_nodes(resource_node.hostname)
            node_labels = node.metadata.labels
            if RG_NODE_TAG_KEY in node_labels:
                del node_labels[RG_NODE_TAG_KEY]
            if RG_TAG_KEY in node_labels and node_labels[RG_TAG_KEY] == resource_node.rg_id:
                del node_labels[RG_TAG_KEY]
            # if the node which be removed is vgpu , then reset the aipods_type label
            key = "aicp.group/aipods_type"
            pre_key = "pre.aicp.group/aipods_type"
            if pre_key in node_labels:
                node_labels[key] = node_labels.pop(pre_key)
            taints = node.spec.taints
            new_taints = []
            if taints:
                for taint in taints:
                    if taint.key not in [RG_TOLERATION_KEY, RG_NODE_TAG_KEY,
                                         RG_NODE_TOLERATION_KEY_SUSPENDED]:
                        new_taints.append(taint)
            node.spec.taints = new_taints
            with Rlock(NODE_TAG_LOCK):
                if not replace_node_tag(resource_node.hostname, node):
                    logger.critical("remove node tag from k8s failed")
                    self.session.rollback()
                    return None
        self.session.commit()
        return True

    def resume_rgn_by_rg_node_ids(self, rg_node_ids: list, user_id=None):
        for rg_node_id in rg_node_ids:
            results = self.session.exec(
                select(ResourceNode).where(ResourceNode.rg_node_id == rg_node_id,
                                           ResourceNode.status == STATUS_SUSPENDED))
            resource_node: ResourceNode = results.one_or_none()
            if not resource_node:
                return None
            if resource_node.billing_order_id:
                if app.settings.billing_enable:
                    billing = QAIBillingService()
                    price_info = ProductCenterResource(resource_node.sku_id, 1).get_billing_price_info()
                    lease_info = None
                    next_charge_mode = None
                    try:
                        lease_info = billing.get_lease_info(resource_node.rg_node_id, self.user.user_id)
                        contract = lease_info["contract"]
                        if contract["charge_mode"] == "elastic":
                            resource_node.during = 3600
                            resource_node.auto_renewal = 0
                        else:
                            resource_node.during = int(str(contract["duration"]).split(" ")[0])
                            next_charge_mode = contract["next_charge_mode"]
                    except Exception as e:
                        logger.info(f"get lease info {lease_info}")
                        logger.error(f"get lease info failed {e}")
                    bill_rep = billing.lease(resource_node.rg_node_id, user_id, price_info,
                                             duration=resource_node.during,
                                             auto_renew=resource_node.auto_renewal,
                                             next_charge_mode=next_charge_mode,
                                             check_resource_balance=True, count=1)
                    if bill_rep['ret_code'] != 0:
                        logger.critical("创建订单失败%s", resource_node.rg_node_id)
                        return None
            resource_node.status = STATUS_ACTIVE
            resource_node.reason = ''
            node = read_nodes(resource_node.hostname)
            taints = node.spec.taints
            new_taints = []
            if taints:
                for taint in taints:
                    if taint.key != RG_NODE_TOLERATION_KEY_SUSPENDED:
                        new_taints.append(taint)
            node.spec.taints = new_taints
            with Rlock(NODE_TAG_LOCK):
                if not replace_node_tag(resource_node.hostname, node):
                    logger.critical("resume node from k8s failed")
                    self.session.rollback()
                    return None
        self.session.commit()
        return True

    def delete_rgn(self, resource_node: ResourceNode):
        node = read_nodes(resource_node.hostname)
        node_labels = node.metadata.labels
        if RG_NODE_TAG_KEY in node_labels:
            del node_labels[RG_NODE_TAG_KEY]
        if RG_TAG_KEY in node_labels and node_labels[RG_TAG_KEY] == resource_node.rg_id:
            del node_labels[RG_TAG_KEY]
        taints = node.spec.taints
        if taints:
            for taint in taints:
                if taint.key in [RG_TOLERATION_KEY, RG_NODE_TOLERATION_KEY, RG_NODE_TOLERATION_KEY_SUSPENDED]:
                    node.spec.taints.remove(taint)
                    break
        with Rlock(NODE_TAG_LOCK):
            if not replace_node_tag(resource_node.hostname, node):
                logger.critical("remove node tag from k8s failed")
                self.session.rollback()
                return None
        return True

    def get_rg(self, rg_id: str, user: QingcloudUser = None):
        resource_group = self.session.get(ResourceGroup, rg_id)
        if resource_group and user:
            if resource_group.user_id != user.user_id:
                stmt = select(ResourceGroupShare).where(ResourceGroupShare.rg_id == rg_id,
                                                        ResourceGroupShare.share_user_id == self.user.user_id)
                resource_group_share_res: ScalarResult[ResourceGroupShare] = self.session.scalars(stmt)
                resource_group_share: ResourceGroupShare = resource_group_share_res.first()
                if not resource_group_share:
                    raise PermissionDeniedException(message=rg_id)
        logger.info("get resource_group [%s]", resource_group)
        return resource_group

    def get_hostnames_by_rg_ids(self, rg_ids: list):
        return self.session.query(ResourceNode.hostname).filter(ResourceNode.rg_id.in_(rg_ids)).all()

    def get_hostnames_by_rgn_ids(self, rgn_ids: list):
        return self.session.query(ResourceNode.hostname).filter(ResourceNode.rg_node_id.in_(rgn_ids)).all()

    def get_rgs(self, rg_id: str, limit=10, offset=0):
        resource_group = self.session.get(ResourceGroup, rg_id)
        logger.info("get resource_group [%s]", resource_group)
        return resource_group

    def get_rgn(self, rg_node_id: str):
        resource_node = self.session.get(ResourceNode, rg_node_id)
        logger.info("get resource_node [%s]", resource_node)
        return resource_node

    def get_rgn_by_ids(self, rg_node_ids: list, limit=10, offset=0):
        if not self.user.is_super_user():
            sql = select(ResourceNode).where(
                ResourceNode.rg_node_id.in_(rg_node_ids), ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED]),
                                                          ResourceNode.user_id == self.user.user_id).limit(
                limit).offset(offset)
        else:
            sql = select(ResourceNode).where(
                ResourceNode.rg_node_id.in_(rg_node_ids), ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED])).limit(
                limit).offset(offset)
        resource_nodes = self.session.exec(sql).fetchall()
        logger.info("get resource_node [%s]", resource_nodes)
        return resource_nodes

    def get_rgn_by_ids_and_user(self, rg_node_ids: list, user_id: str, limit=10, offset=0):
        sql = select(ResourceNode).where(
            ResourceNode.user_id == user_id,
            ResourceNode.rg_node_id.in_(rg_node_ids),
            ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED])).limit(
            limit).offset(offset)
        resource_nodes = self.session.exec(sql).fetchall()
        logger.info("get resource_node [%s]", resource_nodes)
        return resource_nodes

    def get_rg_by_user(self, user: QingcloudUser, order_by="created_at", reverse=False,
                       offset=0, limit=20, search_word=None):
        if user.is_kse_admin_user():
            sql = select(ResourceGroup).where(ResourceGroup.status == STATUS_ACTIVE)
        else:
            sql = select(ResourceGroup).where(ResourceGroup.user_id == user.user_id,
                                              ResourceGroup.status == STATUS_ACTIVE)
        if search_word:
            sql = sql.where(or_(ResourceGroup.name.like(f"%{search_word}%"),
                                ResourceGroup.rg_id.like(f"%{search_word}%")))
        if reverse:
            order_by = getattr(ResourceGroup, order_by).desc()
        else:
            order_by = getattr(ResourceGroup, order_by)
        sql = sql.order_by(order_by)
        sql = sql.offset(offset).limit(limit)
        result = self.session.exec(sql)
        resource_groups = result.fetchall()
        logger.info("get resource_group [%s]", resource_groups)
        return resource_groups

    def get_rg_count_by_user(self, user: QingcloudUser, search_word=None):
        if user.is_kse_admin_user():
            cur = self.session.query(ResourceGroup).filter_by(status=STATUS_ACTIVE)
        else:
            cur = self.session.query(ResourceGroup).filter_by(user_id=user.user_id,
                                                              status=STATUS_ACTIVE)
        if search_word:
            result = cur.where(
                or_(ResourceGroup.name.like(f"%{search_word}%"),
                    ResourceGroup.rg_id.like(f"%{search_word}%"))).count()
        else:
            result = cur.count()
        return result

    def add_rgn(self, data):
        node_ids = []
        for item in data:
            resource_node = ResourceNode(**item.model_dump())
            node_ids.append(resource_node)
            self.session.add(resource_node)
        self.session.commit()
        return node_ids

    def get_rgns(self, rg_id, user: QingcloudUser, limit=10, offset=0):
        if user is None or user.role == USER_ROLE_ADMIN:
            sql = select(ResourceNode).join(
                ResourceGroup, ResourceGroup.rg_id == ResourceNode.rg_id).where(
                ResourceNode.rg_id == rg_id, ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED]),
            )
        else:
            sql = select(ResourceNode).join(
                ResourceGroup, ResourceGroup.rg_id == ResourceNode.rg_id).where(
                ResourceGroup.user_id == user.user_id, ResourceNode.rg_id == rg_id,
                ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED])
            )
        if offset and limit:
            sql = sql.offset(offset).limit(limit)
        result = self.session.exec(sql)
        resource_nodes = result.fetchall()
        logger.info("get resource node [%s]", resource_nodes)
        return resource_nodes

    def get_rgns_and_status(self, user: QingcloudUser, limit=10, offset=0,
                            search_word=None,
                            node_status_filter=None,
                            worker_status_filter=None,
                            aipods_usage=None,
                            order_by="create_at",
                            reverse=1,
                            gpu_model=None,
                            gpu_name=None,
                            rg_id=None,
                            is_reused_gpu_node=None,
                            is_vgpu_node=None):
        if not rg_id:
            sql = select(ResourceNode, ResourceNodeStatus, ResourceGroup) \
                .outerjoin(ResourceNodeStatus, ResourceNode.hostname == ResourceNodeStatus.node_id
                           ).outerjoin(ResourceGroup, ResourceNode.rg_id == ResourceGroup.rg_id) \
                .where(ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED]))
            if not user.is_super_user():
                sql = sql.where(ResourceNode.user_id == user.user_id)
        else:
            rg_share = self.session.query(ResourceGroupShare).filter_by(share_user_id=user.user_id,
                                                                        rg_id=rg_id).one_or_none()
            if rg_share:
                user.user_id = rg_share.owner
            self.user = user
            self.get_by_uuid_with_permission(rg_id)
            sql = select(ResourceNode, ResourceNodeStatus, ResourceGroup).outerjoin(
                ResourceGroup, ResourceGroup.rg_id == ResourceNode.rg_id) \
                .outerjoin(ResourceNodeStatus, ResourceNode.hostname == ResourceNodeStatus.node_id) \
                .where(ResourceNode.status.in_([STATUS_SUSPENDED, STATUS_ACTIVE]),
                       ResourceNode.rg_id == rg_id)
            if not user.is_super_user():
                sql = sql.where(ResourceNode.user_id == user.user_id)
        if search_word:
            sql = sql.where(or_(ResourceNode.name.like(f"%{search_word}%"),
                                ResourceNode.rg_node_id.like(f"%{search_word}%"),
                                ResourceNode.hostname.like(f"%{search_word}%")))
        if gpu_model:
            sql = sql.where(ResourceNode.gpu_model == gpu_model)
        if gpu_name:
            sql = sql.where(ResourceNode.gpu_name == gpu_name)
        if node_status_filter:
            sql = sql.where(ResourceNode.status == node_status_filter)
        if worker_status_filter:
            sql = sql.where(ResourceNodeStatus.status == worker_status_filter)
        if aipods_usage:
            sql = sql.where(ResourceNode.node_type == aipods_usage)
        if is_reused_gpu_node is not None:
            sql = sql.where(ResourceNode.is_reused_gpu_node == is_reused_gpu_node)
        if is_vgpu_node is not None:
            sql = sql.where(ResourceNode.is_vgpu_node == is_vgpu_node)
        if reverse:
            order_by = getattr(ResourceNode, order_by).desc()
        else:
            order_by = getattr(ResourceNode, order_by)
        sql = sql.order_by(order_by)
        sql = sql.offset(offset).limit(limit)
        result = self.session.exec(sql)
        resource_nodes = result.fetchall()
        logger.info("get resource node [%s]", resource_nodes)
        if search_word:
            count = len(resource_nodes)
        else:
            if rg_id:
                count = self.get_rg_node_count(rg_id)
            else:
                if not user.is_super_user():
                    count = self.session.query(ResourceNode).filter_by(user_id=user.user_id,
                                                                       status=STATUS_ACTIVE).count()
                else:
                    count = self.session.query(ResourceNode).filter_by(status=STATUS_ACTIVE).count()
        return resource_nodes, count

    def get_rg_node_count(self, rg_id):
        # sql = select(ResourceGroup.rg_id).where(ResourceGroup.user_id == user_id,
        #                                   ResourceGroup.status == STATUS_ACTIVE)
        result = self.session.query(ResourceNode).filter_by(rg_id=rg_id, status=STATUS_ACTIVE).count()
        return result

    def get_resource_group_node_by_user_id(self, rg_id: str, rg_node_id: str, user_id: str,
                                           gpu_name: str = None) -> ResourceNode:
        """
        检查是否有资源组权限
        :param user_id:
        :param rg_node_id:
        :param rg_id:
        :return:
        """
        rg_stmt = select(ResourceGroup).where(
            ResourceGroup.rg_id == rg_id,
            ResourceGroup.user_id == user_id
        )
        rg = self.session.exec(rg_stmt).first()
        if not rg:
            # 判断是否存在共享资源组
            rg_stmt = select(ResourceGroupShare).where(
                ResourceGroupShare.rg_id == rg_id,
                ResourceGroupShare.share_user_id == user_id
            )
            rg = self.session.exec(rg_stmt).first()
            if not rg:
                raise PermissionDeniedException(f"Resource group {rg_id}")
        rgn_stmt = select(ResourceNode).where(
            ResourceNode.rg_id == rg_id,
            ResourceNode.status.in_([STATUS_ACTIVE, STATUS_SUSPENDED]),
        )
        if rg_node_id:
            rgn_stmt = rgn_stmt.where(ResourceNode.rg_node_id == rg_node_id)
        if gpu_name:
            rgn_stmt = rgn_stmt.where(ResourceNode.gpu_name == gpu_name)
        rgns = self.session.exec(rgn_stmt).all()
        if not rgns:
            if rg_node_id:
                raise PermissionDeniedException(f"Resource node {rg_node_id}")
            raise ResourceGroupNodeNotFoundException(rg_id)
        for rgn in rgns:
            if rgn.status == STATUS_ACTIVE:
                return rgn
        raise ResourceGroupNodeSuspended(rg_id)

    def get_node_status(self, rg_id: str, rg_node_id: str,
                        user: QingcloudUser = None, limit=10, offset=0):
        if rg_node_id:
            if user.role == USER_ROLE_ADMIN:
                resource_node = self.session.get(ResourceNode, rg_node_id)
                if resource_node:
                    node_status = self.session.get(ResourceNodeStatus, resource_node.hostname)
                    return [node_status]
            else:
                resource_node = self.session.get(ResourceNode, rg_node_id)
                resource_group = self.get_rg(resource_node.rg_id, user)
                if resource_group:
                    node_status = self.session.get(ResourceNodeStatus, resource_node.hostname)
                    return [node_status]
                else:
                    return None
        if rg_id:
            resource_nodes = self.get_rgns(rg_id=rg_id, user=user,
                                           limit=limit, offset=offset)
            node_ids = []
            for node in resource_nodes:
                node_ids.append(node.hostname)
            node_status_list = self.get_node_status_by_ids(node_ids)
            return node_status_list
        if user.role == USER_ROLE_ADMIN:
            sql = select(ResourceNodeStatus).offset(offset).limit(limit)
            node_status_list = self.session.exec(sql).fetchall()
            return node_status_list
        return None

    def get_node_status_by_ids(self, node_ids: list):
        sql = select(ResourceNodeStatus).where(
            ResourceNodeStatus.node_id.in_(node_ids))
        node_status_list = self.session.exec(sql).fetchall()
        return node_status_list

    def get_rg_gpus_and_node_count(self, rg_id, user: QingcloudUser = None):
        resource_nodes = self.get_rgns(rg_id=rg_id, user=user,
                                       limit=10000, offset=0)
        node_ids = []
        sku_id = ''
        gpu_model = ''
        for node in resource_nodes:
            sku_id = node.sku_id
            gpu_model = node.gpu_model
            node_ids.append(node.hostname)
        node_status_list = self.get_node_status_by_ids(node_ids)
        total_gpu = 0
        ava_gpu = 0
        for node_status in node_status_list:
            total_gpu += node_status.total_gpu
            ava_gpu += node_status.ava_gpu
        return total_gpu, ava_gpu, node_ids, sku_id, gpu_model

    def get_status_by_uuids(self, resource_ids):
        for resource_id in resource_ids:
            if resource_id.split("-")[0] == "rg":
                stmt = select(ResourceGroup.rg_id, ResourceGroup.status, ResourceGroup.updated_at,
                              ResourceGroup.name, ResourceGroup.reason).where(
                    ResourceGroup.rg_id.in_(resource_ids))
            elif resource_id.split("-")[0] == "rgn":
                stmt = select(ResourceNode.rg_node_id, ResourceNode.status, ResourceNode.updated_at,
                              ResourceNode.name, ResourceNode.reason).where(
                    ResourceNode.rg_node_id.in_(resource_ids))
        resource_status: Sequence[tuple[str, str, datetime, str, str]] = self.session.exec(stmt).all()
        return resource_status

    def get_unschedulable_gpu_count(self, nodes_name: List[str]) -> int:
        if not nodes_name:
            return 0
        return self.session.query(GpuFaultRecords).filter(
            GpuFaultRecords.gpu_node_id.in_(nodes_name),
            GpuFaultRecords.gpu_xid.in_(GpuUnschedulableXids),
            GpuFaultRecords.fault_status == "0",
        ).count()

    def terminate_by_uuid_and_user_id(self, rg_id, user: QingcloudUser = None,
                                      reason="user post"):
        '''
        删除资源组
        :param reason:
        :param check_user: billing notify req
        :param user:
        :param rg_id:
        :param resource_id:
        :return:
        '''
        logger.info("delete resource group by reason [%s]", reason)
        rep = BaseGenericResponse()
        rg_ids = rg_id.split(",")
        namespace = user.user_id.lower()
        rg_node_ids = []
        for rg_id in rg_ids:
            rg = self.session.get(ResourceGroup, rg_id)
            if rg.status == STATUS_TERMINATED:
                continue
            rg.status = STATUS_TERMINATED
            resource_nodes = self.get_rgns(rg_id, user)
            if resource_nodes:
                hostnames = []
                for r in resource_nodes:
                    hostnames.append(r.hostname)
                    # 只删除没有订单的节点
                    if not r.billing_order_id:
                        rg_node_ids.append(r.rg_node_id)
                    node = read_nodes(r.hostname)
                    node_labels = node.metadata.labels
                    if RG_TAG_KEY in node_labels:
                        del node_labels[RG_TAG_KEY]
                    taints = node.spec.taints
                    if taints:
                        for taint in taints:
                            if taint.key == RG_TOLERATION_KEY:
                                node.spec.taints.remove(taint)
                                break
                    with Rlock(NODE_TAG_LOCK):
                        if not replace_node_tag(r.hostname, node):
                            logger.critical("remove node tag from k8s failed")
                            self.session.rollback()
                            return None
                    r.rg_id = r.rg_node_id
                resource = check_user_resource(hostnames, namespace)
                if resource:
                    rep.ret_code = PERMISSION_DENIED
                    rep.message = RESOURCE_IS_RUNNING % resource
                    return rep
            # 如果有和资源组计费绑定的节点，需要删除
            if rg_node_ids:
                self.delete_rgn_by_rg_node_ids(rg_node_ids, user_id=user.user_id)
            # 移除共享信息
            result = self.session.query(ResourceGroupShare).filter(ResourceGroupShare.rg_id == rg_id).all()
            for resource_group_share in result:
                self.session.delete(resource_group_share)
        self.session.commit()
        return rep

    def terminated(self, resource_id, reason=None, force=False):
        '''
        如果是计费调用删除，需要强制清除节点上所有的用户资源
        :param rg_id:
        :param reason:
        :return:
        '''
        logger.info("delete resource  by reason [%s]", reason)
        if resource_id.startswith("rg-"):
            resource_group = self.session.get(ResourceGroup, resource_id)
            namespace = resource_group.user_id.lower()
            if self.session.get(ResourceGroup, resource_id).status == STATUS_TERMINATED:
                return True
            resource_nodes = self.get_rgns(resource_id, self.user)
            if resource_nodes:
                hostnames = []
                for r in resource_nodes:
                    hostnames.append(r.hostname)
                resource = check_user_resource(hostnames, namespace)
                if resource:
                    logger.critical("强制清除用户资源")
            if not self.delete_rg(resource_id, self.user):
                logger.critical("删除用户资源失败")
                return False
            return True
        elif resource_id.startswith("rgn-"):
            resource_node = self.session.get(ResourceNode, resource_id)
            namespace = resource_node.user_id.lower()
            if self.session.get(ResourceNode, resource_id).status == STATUS_TERMINATED:
                return True
            resource = check_user_resource(resource_node.hostname, namespace)
            if resource:
                logger.critical("强制清除用户资源")
            self.delete_rgn(resource_node)  # noqa
            OperationRecord.create_by_resource(resource_node, user_id="Billing").save(session_=self.session)
            if resource_node.billing_order_id:
                if app.settings.billing_enable:
                    billing = QAIBillingService()
                    billing.unlease(resource_id, resource_node.user_id)
            return True

    @log_background_task_exception
    def suspended_by_billing(self, resource_id):
        resource_node: ResourceNode = ResourceNode.one_by_id(resource_id)
        if resource_node.status in (STATUS_SUSPENDED, STATUS_TERMINATED):
            return True
        for _ in range(3):
            node = read_nodes(resource_node.hostname)
            taint = V1Taint(key=RG_NODE_TOLERATION_KEY_SUSPENDED, value=datetime.now().strftime('%Y%m%d%H%M%S'),
                            effect="NoSchedule")
            node.spec.taints.append(taint)
            if replace_node_tag(resource_node.hostname, node):
                break
            logger.error(f"Cordon node [{resource_node.hostname}] failed")
        else:
            logger.critical(f"Cordon node [{resource_node.hostname}] failed 3 times")
            return
        logger.info(f"Cordon node [{resource_node.hostname}] success")
        resource_node.update(status=STATUS_SUSPENDED, reason="欠费暂停")
        OperationRecord.create_by_resource(resource_node, user_id="Billing").save()

    def terminated_by_job(self, resource_id):
        resource_node: ResourceNode = ResourceNode.one_by_id(resource_id)
        namespace = resource_node.user_id.lower()
        if resource_node.status == STATUS_TERMINATED:
            return True
        resource = check_user_resource(resource_node.hostname, namespace)
        if resource:
            logger.critical("强制清除用户资源")
        if resource_node.billing_order_id:
            if app.settings.billing_enable:
                billing = QAIBillingService()
                billing.unlease(resource_id, resource_node.user_id)
        self.delete_rgn(resource_node)
        resource_node.update(status=STATUS_TERMINATED)
        OperationRecord.create_by_resource(resource_node).save()

    def get_by_uuid_with_permission(self, uuid: str):
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(ResourceGroup).where(ResourceGroup.rg_id == uuid)
        if not self.user.is_super_user():
            stmt = stmt.where(ResourceGroup.user_id == self.user.user_id)
        resource_group_res: ScalarResult[ResourceGroup] = self.session.scalars(stmt)
        resource_group: ResourceGroup = resource_group_res.first()

        if not resource_group:
            stmt = select(ResourceGroupShare).where(ResourceGroupShare.rg_id == uuid,
                                                    ResourceGroupShare.share_user_id == self.user.user_id)
            resource_group_share_res: ScalarResult[ResourceGroupShare] = self.session.scalars(stmt)
            resource_group_share: ResourceGroupShare = resource_group_share_res.first()
            if not resource_group_share:
                raise PermissionDeniedException(message=uuid)
        return resource_group

    def get_by_uuids_with_permission(self, uuids: List[str]) -> Sequence[ResourceGroup]:
        """
        通过uuid获取作业
        :param uuids:
        :return:
        """
        stmt = select(ResourceGroup).where(ResourceGroup.rg_id.in_(uuids))
        if not self.user.is_super_user():
            stmt = stmt.where(ResourceGroup.user_id == self.user.user_id)
        resource_groups: ScalarResult[ResourceGroup] = self.session.scalars(stmt)
        return resource_groups.fetchall()

    def get_user_used_disk(self, rgn):
        '''
        查询用户在节点上申请的总容量
        :param rgn:
        :return:
        '''
        notebooks = self.session.query(NotebookReplicaSpec). \
            join(Notebook, NotebookReplicaSpec.notebook_uuid == Notebook.uuid).filter(
            NotebookReplicaSpec.specs == rgn).filter(Notebook.status == "Running").all()
        used_disk = 0
        os_used_disk = 0
        for notebooks in notebooks:
            used_disk = notebooks.custom_data_disk_size + used_disk
            os_used_disk = notebooks.custom_system_disk_size + os_used_disk
        return used_disk, os_used_disk

    def add_resource_group_share_user(self, user, user_ids, rg_id, is_all):
        '''
        添加共享用户
        :param user:
        :param user_ids:
        :param rg_id:
        :return:
        '''
        if is_all:
            user_ids = []
            rep = describe_sub_users(user.user_id,
                                     user.access_key_id,
                                     user.secret_access_key,
                                     0, 100)
            subusers = rep.get("user_set", [])
            for subuser in subusers:
                user_ids.append(subuser["user_id"])
        for user_id in user_ids:
            resource_group_share = ResourceGroupShare(share_user_id=user_id,
                                                      rg_id=rg_id,
                                                      owner=user.user_id)
            shared = self.session.query(ResourceGroupShare).filter_by(rg_id=rg_id, share_user_id=user_id).all()
            if not shared:
                self.session.add(resource_group_share)
            else:
                logger.info(" rg [%s] has been shared by user [%s]", rg_id, user_id)

    def get_resource_group_share_user(self, rg_id, offset, limit):
        '''
        查询已共享用户
        :param owner:
        :param user_ids:
        :param rg_id:
        :return:
        '''
        shared = self.session.query(ResourceGroupShare).filter_by(rg_id=rg_id).offset(offset).limit(limit).all()
        logger.info("share group %s", shared)
        user_ids = []
        data = {}
        for share in shared:
            user_ids.append(share.share_user_id)
        if user_ids:
            data = describe_user_without_redis(user_ids)
        logger.info("share user [%s]", user_ids)
        count = self.session.query(ResourceGroupShare).filter_by(rg_id=rg_id).count()
        return data, count

    def get_resource_group_share_all_sub_user(self, offset, limit):
        '''
        查询已共享用户
        :param owner:
        :param user_ids:
        :param rg_id:
        :return:
        '''
        shared = self.session.query(ResourceGroupShare).filter(ResourceGroupShare.owner == self.user.user_id).all()
        all_user_ids = []
        data = {}
        logger.info("share group %s", shared)
        for share in shared:
            all_user_ids.append(share.share_user_id)
        user_ids = list(set(all_user_ids))
        if user_ids and len(user_ids) > offset:
            if user_ids[offset:limit + offset]:
                logger.info("share user [%s]", user_ids[offset:limit + offset])
                data = describe_user_without_redis(user_ids[offset:limit + offset])

        count = len(user_ids)
        return data, count

    def remove_resource_group_share_user(self, rg_id, is_all=0, user_ids=None):
        '''
        查询已共享用户
        :param is_all:
        :param owner:
        :param user_ids:
        :param rg_id:
        :return:
        '''
        if is_all:
            self.session.exec(delete(ResourceGroupShare).where(ResourceGroupShare.rg_id == rg_id))
        else:
            for user_id in user_ids:
                shared_list = self.session.query(ResourceGroupShare).filter_by(rg_id=rg_id, share_user_id=user_id).all()
                for shared in shared_list:
                    self.session.delete(shared)
        return

    def get_resource_group_share(self, user_id):
        '''
        查询已共享用户
        :param user_id:
        :return:
        '''
        shared = self.session.query(ResourceGroupShare).filter_by(share_user_id=user_id).all()
        resource_groups = []
        for share in shared:
            resource_group = self.get_rgs(share.rg_id)
            resource_groups.append(resource_group)
        return resource_groups

    def add_resource_template(self, req: CreateResourceTemplateReq, user: QingcloudUser):
        tmp_ids = []
        for item in req.data:
            if not self.get_rg(item.rg_id, user):
                raise ResourceGroupNotFoundException(item.rg_id)
            if not item.pod_type:
                pod_type = ','.join(sorted(list(pod_type_emu)))
            else:
                pod_type_list = [p.value for p in item.pod_type]
                if pod_type_emu.issuperset(set(pod_type_list)):
                    pod_type = ','.join(sorted(pod_type_list))
                else:
                    raise ParameterException(item.pod_type)
            if item.spec_type == TemplateSpecType.vGPU and not item.gpu_memory:
                raise ParameterException("vGpu memory is required when spec_type is vGPU")
            if item.gpu_list:
                item.gpu = len(item.gpu_list)

            resource_template = ResourceTemplate(
                **{
                    **item.dict(),
                    **{"pod_type": pod_type, "user_id": user.user_id, "root_user_id": user.root_user_id}
                }
            )
            self.session.add(resource_template)
            self.session.commit()
            tmp_ids.append(resource_template.tmp_id)
        return tmp_ids

    def update_resource_template(self, req: UpdateResourceTemplateReq, user: QingcloudUser):
        for req_template in req.data:
            resource_template: ResourceTemplate = self.session.query(ResourceTemplate).filter_by(
                tmp_id=req_template.tmp_id).one_or_none()
            if req_template.gpu_list:
                req_template.gpu = len(req_template.gpu_list)
            if resource_template:
                for key, value in req_template.dict().items():
                    if key == "pod_type":
                        pod_type_list = [p.value for p in value]
                        if pod_type_emu.issuperset(set(pod_type_list)):
                            value = ','.join(sorted(pod_type_list))
                        else:
                            raise ParameterException(value)
                    if hasattr(resource_template, key):
                        setattr(resource_template, key, value)
            else:
                pod_type_list = [p.value for p in req_template.pod_type]
                resource_template = ResourceTemplate(
                    cpu=resource_template.cpu,
                    memory=resource_template.memory,
                    name=resource_template.name,
                    gpu=resource_template.gpu,
                    gpu_memory=resource_template.gpu_memory,
                    ib=resource_template.ib,
                    pod_type=",".join(pod_type_list),
                    rg_id=resource_template.rg_id,
                    user_id=user.user_id,
                    root_user_id=user.root_user_id,
                    gpu_list=resource_template.gpu_list,
                    show_user=resource_template.show_user,
                    rg_node_id=resource_template.rg_node_id
                )
                self.session.add(resource_template)
            self.session.commit()
        return True

    def get_resource_template(self, query: ResourceTemplateQuery):
        order_by_key = getattr(ResourceTemplate, query.order_by)
        order_by = desc(order_by_key) if query.reverse else asc(order_by_key)

        template_filters = []
        if query.template_ids:
            template_filters.append(ResourceTemplate.tmp_id.in_(query.template_ids))

        if query.pod_type:
            pod_type_list = [p.value for p in query.pod_type]
            if pod_type_emu.issuperset(set(pod_type_list)):
                pod_type_str_like = f"%{'%'.join(sorted(pod_type_list))}%"
                template_filters.append(ResourceTemplate.pod_type.like(pod_type_str_like))
            else:
                raise ParameterException(query.pod_type)

        if query.spec_type:
            template_filters.append(ResourceTemplate.spec_type.in_(query.spec_type))

        if query.rg_node_id:
            template_filters.append(ResourceTemplate.rg_node_id == query.rg_node_id)

        if not query.rg_id:
            return self.session \
                .query(ResourceTemplate, ResourceGroup) \
                .outerjoin(ResourceGroup, ResourceTemplate.rg_id == ResourceGroup.rg_id) \
                .filter(ResourceTemplate.user_id == self.user.user_id, *template_filters) \
                .order_by(order_by)\
                .limit(query.limit).offset(query.offset)\
                .all()

        template_filters.append(ResourceTemplate.rg_id == query.rg_id)
        if self.session.query(ResourceGroupShare).filter_by(share_user_id=self.user.user_id,
                                                            rg_id=query.rg_id).one_or_none():
            template_filters.append(or_(
                ResourceTemplate.show_user.any(self.user.user_id),
                ResourceTemplate.show_user.is_(None)
            ))
            return self.session \
                .query(ResourceTemplate, ResourceGroup) \
                .outerjoin(ResourceGroup, ResourceTemplate.rg_id == ResourceGroup.rg_id) \
                .filter(*template_filters) \
                .order_by(order_by)\
                .limit(query.limit).offset(query.offset)\
                .all()

        template_filters.append(ResourceTemplate.user_id == self.user.user_id)
        return self.session \
            .query(ResourceTemplate, ResourceGroup) \
            .outerjoin(ResourceGroup, ResourceTemplate.rg_id == ResourceGroup.rg_id) \
            .filter(*template_filters) \
            .order_by(order_by)\
            .limit(query.limit).offset(query.offset)\
            .all()

    def get_template_by_rg_id(self, rg_id):
        """
        查询资源组相关的资源模板
        :param rg_id:
        :return:
        """
        return self.session.query(ResourceTemplate).filter_by(rg_id=rg_id).all()

    def get_template_by_rg_id_and_rgn_ids(self, rg_id, rgn_ids):
        """
        查询资源组相关的资源模板
        :param rg_id:
        :return:
        """
        return self.session.query(ResourceTemplate) \
            .filter(ResourceTemplate.rg_id == rg_id, ResourceTemplate.rg_node_id.in_(rgn_ids)).all()

    def get_template_on_rgn_ids(self, rgn_ids: list[str]):
        """
        查询节点相关的资源模板
        :param rgn_ids:
        :return:
        """
        return self.session.query(ResourceTemplate).filter(ResourceTemplate.rg_node_id.in_(rgn_ids)).all()

    def get_reused_gpu_template_on_rgn_id(self, rgn_id):
        """
        查询重用的gpu节点模板, find rg_node_id = rgn_id and (gpu_list is not None or gpu_list is not empty)
        :param rgn_id:
        :return:
        """
        return list(filter(lambda x: x.gpu_list, self.get_template_on_rgn_ids([rgn_id])))

    def delete_resource_template(self, id_list, user):
        for tmp_id in id_list:
            resource_template = self.session.query(ResourceTemplate).filter_by(tmp_id=tmp_id,
                                                                               user_id=user.user_id).one_or_none()
            if resource_template:
                self.session.delete(resource_template)
            else:
                logger.warning("trmplate[%s] not found", tmp_id)
        return True

    def get_rg_node_with_user_permission(self, user_id, rg_id, rg_node_id) -> Optional[ResourceNode]:
        '''
        查询资源节点
        :param user_id:
        :param rg_id:
        :param rg_node_id:
        :return: ResourceNode
        '''
        # 查询用户是否有资源组的权限, 自己的或者共享的
        resource_group = ResourceGroup.first_by_fields(fields=dict(user_id=user_id, rg_id=rg_id), session_=self.session)
        if resource_group is None:
            resource_group = ResourceGroupShare.first_by_fields(fields=dict(share_user_id=user_id, rg_id=rg_id),
                                                                session_=self.session)
            if resource_group is None:
                return None
        filters = dict(rg_id=rg_id, status="active")
        if rg_node_id:
            filters["rg_node_id"] = rg_node_id
        resource_node = ResourceNode.first_by_fields(fields=filters, session_=self.session)
        return resource_node

    def get_rgn_by_rgs_id(self, rgs_id: list[str]) -> Sequence[ResourceNode]:
        stmt = select(ResourceNode).where(ResourceNode.rg_id.in_(rgs_id))
        return self.session.exec(stmt).fetchall()

    @log_background_task_exception
    def resume_by_billing(self, uuid: str):
        """
        恢复节点
        :param uuid:
        :return:
        """
        logger.info("resume_by_billing[%s] ,but only print log", uuid)
        return None
