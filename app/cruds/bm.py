import base64
import time
import traceback
from datetime import datetime
from typing import List, Sequence

from kubernetes.client import Api<PERSON>x<PERSON>, V1Secret, V1ObjectMeta
from sqlmodel import Session, select, func, desc

from app import logger, settings
from app.core.constant import RESOURCE_BM, BM_GROUP, BM_VERSION, BM_KIND, STATUS_TERMINATED, STATUS_SUCCESS
from app.core.exceptions import ResourceNotFoundException, PermissionDeniedException
from app.core.kube.api import create_custom_rsrc, get_custom_rsrc, delete_custom_rsrc, update_custom_rsrc, \
    create_secret, get_secret
from app.core.kube.api.subnet import get_subnet, create_subnet, SubnetAllocator
from app.core.models import QingcloudUser
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.common import send_to_push_server
from app.core.qingcloud.interface import ZONE_INFO, product_center_query_request
from app.core.utils import generate_random_string
from app.cruds.operation_record import OperationR<PERSON>ordCrud
from app.models.bm import CreateBmInstanceRequest, BmInstance, CreateBmInstanceResponse, UpdateBmInstanceRequest, \
    BmImage, CreateBmImageRequest
from app.models.net import Subnet


class BMCrud:
    """
    Train CRUD
    """

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session: Session = session
        self.user = user

    def generate_id(self, exclude_ids=None):
        while True:
            bm_node_id = "bm-" + generate_random_string().lower()
            logger.info("get bm_node_id [%s]", bm_node_id)
            bm_instance = self.session.exec(select(BmInstance).where(BmInstance.bm_node_id==bm_node_id)).one_or_none()
            if bm_instance:
                continue
            if exclude_ids and bm_node_id in exclude_ids:
                continue
            return bm_node_id

    def generate_image_id(self, exclude_ids=None):
        while True:
            bm_image_id = "img-" + generate_random_string()
            logger.info("get bm_image [%s]", bm_image_id)
            bm_image = self.session.exec(select(BmImage).where(BmImage.bm_image_id==bm_image_id)).one_or_none()
            if bm_image:
                continue
            if exclude_ids and bm_image_id in exclude_ids:
                continue
            return bm_image_id


    def create_bm_instance(self, bm_instances: List[BmInstance]):
        try:
            logger.info("start create bm_instance [%s]", bm_instances)
            namespace = bm_instances[0].user_id.lower()
            subnet =None
            try:
                subnet_name = bm_instances[0].user_id
                subnet = get_subnet(subnet_name)
            except  ApiException as e:
                if e.status == 404:
                    logger.info("subnet [%s] not exit ,will be create", subnet_name)
            logger.info("111111111111111111111111111111")
            if not subnet:
                sql = select(Subnet).order_by(desc(Subnet.cidrBlock)).limit(1)
                latest_subnet = self.session.exec(sql).one_or_none()
                logger.info("2222222222222222222222222222")
                if latest_subnet:
                    allocator = SubnetAllocator(current_subnet=latest_subnet.cidrBlock).get_next_subnet()
                else:
                    allocator = SubnetAllocator().get_next_subnet()
                create_subnet(namespace, subnet_name, allocator)
                subnet_has_created = False
                logger.info("3333333333333333333333333333333")
                for i in range(60):
                    logger.info("waiting create subnet [%s]", subnet_name)
                    try:
                        result= get_subnet(subnet_name)
                        if result:
                            subnet_has_created = True
                            self.session.add(Subnet(name=subnet_name,cidrBlock=allocator,vpc='zd-vpc',namespaces=[namespace]))
                            logger.info("create subnet [%s] success", subnet_name)
                            break
                    except ApiException as e:
                        if e.status == 404:
                            time.sleep(1)
                if not subnet_has_created:
                    logger.error("create subnet [%s] failed, create bm broken ....", subnet_name)
                    return
            bm_status = {}
            secret_data = {
                "username": base64.b64encode(bm_instances[0].username.encode("utf-8")).decode("utf-8"),
                "password": base64.b64encode(bm_instances[0].password.encode("utf-8")).decode("utf-8")
            }
            secret_name = '-'.join([i.bm_node_id for i in bm_instances])
            create_secret(namespace, secret_data, secret_name)
            create_secret_result = False
            for i in range(120):
                try:
                    result = get_secret(namespace, secret_name)
                    if result:
                        create_secret_result = True
                        break
                except ApiException as e:
                    if e.status == 404:
                        time.sleep(1)
            if not create_secret_result:
                logger.error("create secret [%s] failed, create bm broken ....", secret_name)
                return
            for bm in bm_instances:
                bm_status[bm.bm_node_id] = ''
                data = {
                    "apiVersion": f"{BM_GROUP}/{BM_VERSION}",
                    "kind": "Instance",
                    "metadata": {
                        "name": bm.bm_node_id,
                        "namespace": namespace,
                        # "labels": {
                        #     "notebook": notebook.uuid,
                        #     "user": notebook.user_id
                        # }
                    },
                    "spec": {
                        "nodeSelector":{
                            "aicp.group/aipods_type": "bm"
                        },
                        "namespace": namespace,
                        "image": "focal5uefix64",
                        "cleanMode": "fast",
                        "loginMode": {
                          "auths": ["sirius"] ,
                          "auth": "keypair"
                        },
                        "network": {
                            "subnet": subnet_name,
                        },
                        "state": "running",
                        "tolerations":[
                            {
                                "effect": "NoSchedule",
                                "key": "aicp.group/worker",
                                "operator": "Exists"
                            }
                        ]
                    }
                }
                r = create_custom_rsrc(group=BM_GROUP,
                                   version=BM_VERSION, kind=BM_KIND,
                                   data=data, namespace=namespace)
                logger.info("create bm instance rep [%s]", r)
            n = 3600
            op_session = OperationRecordCrud(session=self.session)
            op_id = op_session.generate_id()
            result_status = ["failed","running"]
            while n > 0:
                time.sleep(10)
                for bm in bm_instances:
                    try:
                        result = get_custom_rsrc(group=BM_GROUP, version=BM_VERSION, kind=BM_KIND, namespace=self.user.user_id.lower(),
                                                 name=bm.bm_node_id)
                    except Exception as e:
                        logger.warning("get bm instance failed [%s]", e)
                        continue
                    logger.debug("create bm instance result[%s]", result["status"]["phase"])
                    if result and "status" in result:
                        if result["status"]["phase"] !=  bm_status[bm.bm_node_id]:
                            status = result["status"]["phase"]
                            bm_status[bm.bm_node_id] = status
                            # send_to_push_server(action="创建bm主机", resource=bm.bm_node_id,
                            #                     status=status, reason=result["status"].get("deployTasks"),
                            #                     user_id=self.user.user_id,
                            #                     op_id=op_id,
                            #                     resource_type=RESOURCE_BM)
                        if result["status"]["phase"] in ["running"]:
                            logger.info("create bm instance successful [%s]", bm.bm_node_id)
                        if result["status"]["phase"] in ["failed"]:
                            logger.error("create bm instance failed [%s]", bm.bm_node_id)
                    bm.status = result["status"]["phase"]
                if all(value in result_status for value in bm_status.values()):
                    logger.info("create bm instance finish")
                    break
                n = n - 10
            self.session.commit()
        except Exception as e:
            logger.error(traceback.format_exc())
            logger.error("create bm instance failed [%s]", e)

    def create_bm1_instance(self, bm_instances: List[BmInstance]):
        '''
        仅仅创建数据库记录，等待手动装机完成之后，创建订单开始计费
        :param bm_instances:
        :return:
        '''


    def get_bm_instance(self, offset=0, limit=10, order_by="created_at", reverse=1, status=None):
        if status is None:
            status = list()
        if self.user.is_super_user():
            statement = select(BmInstance).where(BmInstance.status.in_(status))
            count = self.session.exec(
                select(func.count()).select_from(BmInstance).where(BmInstance.status.in_(status))).first()
        else:
            count = self.session.exec(select(func.count()).select_from(BmInstance).where(
                BmInstance.user_id == self.user.user_id).where(BmInstance.status.in_(status))).first()
            statement = select(BmInstance).where(BmInstance.user_id==self.user.user_id,
                                                 BmInstance.status.in_(status))
        statement = statement.offset(offset).limit(limit)
        if reverse:
            statement = statement.order_by(getattr(BmInstance, order_by).desc())
        else:
            statement = statement.order_by(getattr(BmInstance, order_by).asc())
        bm_instances = self.session.exec(statement).all()
        return bm_instances, count

    def get_bm_instance_by_ids(self, bm_node_ids):
        statement = select(BmInstance).where(BmInstance.bm_node_id.in_(bm_node_ids))
        if not self.user.is_super_user():
            statement = statement.where(BmInstance.user_id==self.user.user_id)
        bm_instances = self.session.exec(statement).all()
        return bm_instances


    def get_status_by_uuids(self, resource_ids):
        statement = select(BmInstance.bm_node_id, BmInstance.status, BmInstance.updated_at, BmInstance.name,
                           BmInstance.reason
                           ).where(BmInstance.bm_node_id.in_(resource_ids))
        bm_status: Sequence[tuple[str, str, datetime, str, str]] = self.session.exec(statement).all()
        return bm_status

    def delete_bm_instances(self, bm_instances: List[BmInstance]):
        try:
            bm_status = {}
            for bm_instance in bm_instances:
                if not self.terminated(bm_instance.bm_node_id):
                    logger.error("delete bm instance failed [%s]", bm_instance.bm_node_id)
                    continue
                if settings.billing_enable:
                    billing = QAIBillingService()
                    billing.unlease(bm_instance.bm_node_id, self.user.user_id)
                if bm_instance.bm_type != "bm1":
                    bm_status[bm_instance.bm_node_id] = bm_instance.status
            n = 3600
            op_session = OperationRecordCrud(session=self.session)
            op_id = op_session.generate_id()
            while n > 0:
                if not bm_status:
                    break
                time.sleep(10)
                for bm_instance in bm_instances:
                    try:
                        get_custom_rsrc(group=BM_GROUP, version=BM_VERSION, kind=BM_KIND, namespace=self.user.user_id.lower(),
                                        name=bm_instance.bm_node_id)
                    except Exception as e:
                        # 删除成功，推送结果并更新资源状态
                        if e.status == 404:
                            logger.info("delete bm instance successful")
                            del bm_status[bm_instance.bm_node_id]
                            # send_to_push_server(action="删除bm主机", resource=bm_instance.bm_node_id,
                            #                     status=STATUS_SUCCESS, reason='',
                            #                     user_id=self.user.user_id,
                            #                     op_id=op_id,
                            #                     resource_type=RESOURCE_BM)
                            bm_instance.status = STATUS_TERMINATED
                n = n - 10
            self.session.commit()
            return
        except Exception as e:
            logger.error(traceback.format_exc())


    def terminated(self, resource_id):
        bm_instance: BmInstance= self.session.exec(select(BmInstance).where(BmInstance.bm_node_id==resource_id)).one_or_none()
        namespace = bm_instance.user_id.lower()
        if bm_instance.bm_type == "bm1":
            bm_instance.status = STATUS_TERMINATED
            return True
        # if bm_instance.status not in ["running", "stopped","failed"]:
        #     logger.error("instance status not allowed to terminated")
        #     return False
        bm_resource = get_custom_rsrc(group=BM_GROUP, version=BM_VERSION, kind=BM_KIND, namespace=namespace,
                        name=bm_instance.bm_node_id)
        if not bm_instance:
            logger.warning("bm instance not exits [%s] ns[%s]", bm_instance.bm_node_id, self.user.user_id.lower())
        bm_resource["spec"]["state"] = "deleted"
        update_custom_rsrc(BM_GROUP, BM_VERSION, BM_KIND, resource_id, bm_resource, namespace)
        delete_custom_rsrc(BM_GROUP, BM_VERSION, BM_KIND, resource_id, namespace)
        return True

    def update_bm_instance(self, req: UpdateBmInstanceRequest):
        bm_instance: BmInstance = self.session.exec(select(BmInstance).where(BmInstance.bm_node_id == req.bm_node_id,
                                                                             BmInstance.user_id == self.user.user_id
                                                                             )).one_or_none()
        if not bm_instance:
            raise ResourceNotFoundException(req.bm_node_id)
        # if bm_instance.status != req.status:
            # update_custom_rsrc(BM_GROUP, BM_VERSION, BM_KIND, req.bm_node_id, bm_instance.user_id.lower())
        bm_instance.name = req.name
        self.session.commit()
        return bm_instance


    def release_bm_instance(self, bm_node_id: str):
        if not self.user.is_super_user():
            raise PermissionDeniedException(bm_node_id)
        bm_instance: BmInstance = self.session.exec(select(BmInstance).where(BmInstance.bm_node_id == bm_node_id,)).one_or_none()
        if not bm_instance:
            raise ResourceNotFoundException(bm_node_id)
        if bm_instance.status == "pending":
            bm_instance.status = "running"
        if settings.billing_enable:
            billing = QAIBillingService()
            billing.unlease(bm_instance.bm_node_id, self.user.user_id)
        self.session.commit()
        return bm_instance

    def get_bm_image(self, offset=0, limit=10, order_by="create_at", reverse=1, status=None):
        if status is None:
            status = list()
        if self.user.is_super_user():
            statement = select(BmImage).where(BmImage.status.in_(status))
            count = self.session.exec(
                select(func.count()).select_from(BmImage).where(BmImage.status.in_(status))).scalar()
        else:
            count = self.session.exec(select(func.count()).select_from(BmImage).where(
                BmImage.user_id == self.user.user_id).where(BmImage.status.in_(status))).scalar()
            statement = select(BmImage).where(BmImage.status.in_(status))
        statement = statement.offset(offset).limit(limit)
        if reverse:
            statement = statement.order_by(getattr(BmImage, order_by).desc())
        else:
            statement = statement.order_by(getattr(BmImage, order_by).asc())
        bm_images = self.session.exec(statement).all()
        return bm_images, count


    def create_bm_image(self, req: CreateBmImageRequest):
        bm_image_id = self.generate_image_id()
        bm_image = BmImage(**req.model_dump())
        bm_image.bm_image_id = bm_image_id
        self.session.add(bm_image)
        #### create crd resource ###
        #pass
        return bm_image

    def delete_bm_image(self, bm_image_ids):
        bm_images: Sequence[BmImage]= self.session.exec(select(BmImage).where(BmImage.bm_image_id.in_(bm_image_ids))).all()
        for bm_image  in bm_images:
            self.session.delete(bm_image)
            delete_custom_rsrc(BM_GROUP, BM_VERSION, BM_KIND, bm_image.bm_image_id, "default")
        return bm_images


    def update_bm_image(self):
        pass




