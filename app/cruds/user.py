from typing import Optional, Tuple, Sequence, List

from kr8s import NotFoundError
from kr8s.objects import Namespace, RoleBinding
from sqlalchemy import func, ScalarR<PERSON>ult, desc, text
from sqlmodel import Session, or_, select, col
import app
from app import logger
from app.apps.notebooks.exceptions import NotebookQuotaException
from app.core.constant import UserFrom
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.core.qingcloud.docker_api import QingcloudDockerApiClient
from app.core.qingcloud.gpfs import QingcloudGpfsClient
from app.core.qingcloud.maas import MaasClient
from app.core.qingcloud.resource import ProductCenterResource
from app.models.notebooks import NotebookStatus, Notebook, NotebookReplicaSpec
from app.models.resource_group import ResourceNode
from app.models.trains import Train, TrainReplicaSpec
from app.models.trains import TrainStatus
from app.models.user import <PERSON>o<PERSON><PERSON>, ProjectCategory, ProjectCategoryBase, SharedPermissionEnum, UserInfo, \
    UserStatusEnum
from app.apps.user.requests import AicpUserQueryParams
from app.core.exceptions import PermissionDeniedException, ResourceNotFoundException
from app.core.kube.kr8s import AuthorizationPolicy
from app.core.models import QingcloudUser
from app.core.qingcloud.interface import describe_users, product_center_query_request


class UserCRUD:
    """
    User CRUD
    """

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session = session
        self.user = user

    def create_minio_key(self, minio_key: MinioKey):
        """
        创建minio key
        :param minio_key:
        """
        self.session.add(minio_key)
        self.session.commit()
        self.session.refresh(minio_key)

    def get_user_count(self) -> int:
        statement = select(func.count(UserInfo.user_id))
        count = self.session.scalars(statement)
        return count.one()

    def batch_insert_user(self, user_info: List[UserInfo]):
        self.session.bulk_save_objects(user_info)
        self.session.commit()

    def get_user_info_by_user_id(self, user_id) -> UserInfo:
        return self.session.query(UserInfo).filter_by(user_id=user_id).first()

    def page_users(self, query: AicpUserQueryParams) -> Tuple[int, Sequence[UserInfo]]:
        # combine SQL
        count_st = select(func.count(UserInfo.namespace))
        statement = select(UserInfo).order_by(desc(UserInfo.updated_at)).offset(query.offset).limit(query.limit)
        if query.type == "id" and query.input is not None and query.input != "":
            fuzzy_query = col(UserInfo.namespace).contains(f"%{query.input.lower()}%")
            count_st = count_st.where(fuzzy_query)
            statement = statement.where(fuzzy_query).order_by(desc(UserInfo.updated_at))
        count = self.session.scalars(count_st)
        users: ScalarResult[UserInfo] = self.session.scalars(statement)
        return count.one(), users.fetchall()

    def init_user_info(self, user_id: str, namespace: str):
        user = self.session.query(UserInfo).filter_by(user_id=user_id).first()
        if user:
            logger.info(f"User already exists.")
            return
        user_init = UserInfo()
        user_init.user_id = user_id
        user_init.namespace = namespace
        user_init.containers_number = app.settings.POD_NUMBER
        user_init.jobs_number = app.settings.JOB_NUMBER
        user_init.rg_nodes = app.settings.RG_NODES
        user_init.maas_server = app.settings.MAAS_SERVER
        # profile not initialization
        user_init.status = "warning"
        self.session.add(user_init)
        self.session.commit()

    def update_user_info_status(self, user_id: str, status: str):
        user: UserInfo = self.get_user_info_by_user_id(user_id)
        user.status = status
        self.session.commit()

    # profile have info and db not exists
    def exist_user_info_add(self, user_id: str, namespace: str):
        user = self.session.query(UserInfo).filter_by(user_id=user_id).first()
        if user:
            logger.info(f"User already exists.")
            if user.status == UserStatusEnum.inactive:
                user.gpu_number = app.settings.GPU_NUMBER
                user.vgpu_number = app.settings.GPU_NUMBER
                user.status = UserStatusEnum.active
                self.session.commit()
            return
        user_init = UserInfo()
        user_init.user_id = user_id
        user_init.namespace = namespace
        user_init.containers_number = app.settings.POD_NUMBER
        user_init.jobs_number = app.settings.JOB_NUMBER
        user_init.rg_nodes = app.settings.RG_NODES
        user_init.maas_server = app.settings.MAAS_SERVER
        user_init.dir_number = app.settings.DIR_NUMBER
        user_init.capacity = app.settings.CAPACITY
        user_init.docker_quota = app.settings.DOCKER_QUOTA
        user_init.project_number = app.settings.PROJECT_NUMBER
        user_init.gpu_number = app.settings.GPU_NUMBER
        user_init.vgpu_number = app.settings.GPU_NUMBER
        # profile initialization
        user_init.status = UserStatusEnum.active
        self.session.add(user_init)
        self.session.commit()

    def update_quota(self, user_id: str, containers_number, jobs_number, rg_nodes, priority, dir_number, capacity,
                     maas_server, gpu_number, vgpu_number, docker_quota, gpu_model_quotas, vgpu_model_quotas,
                     project_number):
        user: UserInfo = self.get_user_info_by_user_id(user_id)
        if not user:
            self.init_user_info(user_id=user_id, namespace=user_id.lower())
            user: UserInfo = self.get_user_info_by_user_id(user_id)
        if isinstance(containers_number, int):
            user.containers_number = containers_number
        if isinstance(jobs_number, int):
            user.jobs_number = jobs_number
        if isinstance(rg_nodes, int):
            user.rg_nodes = rg_nodes
        if isinstance(priority, int):
            user.priority = priority
        if isinstance(dir_number, int):
            user.dir_number = dir_number
        if isinstance(capacity, int):
            user.capacity = capacity
        if isinstance(maas_server, int):
            user.maas_server = maas_server
        if isinstance(gpu_number, int):
            user.gpu_number = gpu_number
        if isinstance(vgpu_number, int):
            user.vgpu_number = vgpu_number
        if isinstance(docker_quota, int):
            user.docker_quota = docker_quota
        if gpu_model_quotas is not None:
            if gpu_model_quotas == {'all': -1}:
                # 不限制配额
                user.gpu_model_quotas = {key: -1 for key in user.gpu_model_quotas}
            else:
                user.gpu_model_quotas = gpu_model_quotas  # 否则直接覆盖
        if vgpu_model_quotas is not None:
            if vgpu_model_quotas == {'all': -1}:
                # 不限制配额
                user.vgpu_model_quotas = {key: -1 for key in user.vgpu_model_quotas}
            else:
                user.vgpu_model_quotas = vgpu_model_quotas
        if isinstance(project_number, int):
            user.project_number = project_number
        self.session.commit()

    # quota verification
    def quota_verification(self, user_id: str, type: str, num: int, specs: str) -> bool:
        user: UserInfo = self.session.query(UserInfo).filter_by(user_id=user_id).first()
        if not user:
            user = UserInfo(containers_number=10, jobs_number=10, rg_nodes=10)

        active_resources = {
            "nb": self.__get_active_notebook(user_id),
            "tn": self.__get_active_train(user_id),
            "rg": self.__get_active_resource_node(user_id)
        }
        # 汇总当前用户所使用的gpu&vgpu数据
        gpu_specs, vgpu_specs = self._get_gpu_specs(active_resources)

        # 获取maas服务数据
        maas_data = MaasClient().get_maas_server_num(user_id)
        self._merge_maas_resource_usage(gpu_specs, maas_data.get(user_id, {}).get("gpu", {}))
        self._merge_maas_resource_usage(vgpu_specs, maas_data.get(user_id, {}).get("vgpu", {}))

        # 检查用户配额
        self._check_basic_quotas(user, type, num, active_resources)

        if specs.startswith("sku_"):
            production = get_gpu_referred_sku(specs)
            if not production:
                return True
            gpu_type = production.aipods_type.value
            gpu_model = None
            if production.gpu_memory:
                gpu_memory = production.gpu_memory.name.rstrip("G")
                gpu_model = f"{production.gpu_model.attr_value} {gpu_memory}"

            if production.gpu_memory:
                # 检查 gpu 或 vgpu配额
                num = int(production.gpu_count.attr_value) * num
                logger.info(f"the current number of GPU is {production.gpu_count.attr_value}, {num}")
                self._check_gpu_quotas(user, gpu_specs, vgpu_specs, num, gpu_type, gpu_model)



    def __get_active_notebook(self, user_id: str) -> Sequence[Notebook]:
        stmt = select(Notebook).where(
            (Notebook.namespace == user_id.lower()) &
            (Notebook.status.in_([
                NotebookStatus.Pending, NotebookStatus.Creating,
                NotebookStatus.Created, NotebookStatus.Running,
                NotebookStatus.Succeeded, NotebookStatus.Suspending,
                NotebookStatus.Suspended
            ]))
        )
        return self.session.scalars(stmt).fetchall()

    def __get_active_train(self, user_id: str) -> Sequence[Train]:
        stmt = select(Train).where(
            (Train.namespace == user_id.lower()) &
            (Train.status.in_([
                TrainStatus.Pending, TrainStatus.Creating,
                TrainStatus.Created, TrainStatus.Running,
                TrainStatus.Suspended, TrainStatus.Suspending
            ]))
        )
        return self.session.scalars(stmt).fetchall()

    def __get_active_resource_node(self, user_id: str) -> Sequence[ResourceNode]:
        stmt = select(ResourceNode).where(
            (ResourceNode.user_id == user_id) &
            (ResourceNode.status == "active")
        )
        return self.session.scalars(stmt).fetchall()

    def _get_gpu_specs(self, active_resources: dict) -> tuple[dict[str, int], dict[str, int]]:
        gpu_specs = {}
        vgpu_specs = {}
        if not active_resources:
            return gpu_specs, vgpu_specs
            # 查询实例下所有gpu和vgpu数据
        if notebooks := active_resources.get("nb"):
            notebook_uuids = [nb.uuid for nb in notebooks]
            stmt = select(NotebookReplicaSpec).where(
                NotebookReplicaSpec.notebook_uuid.in_(notebook_uuids),
                NotebookReplicaSpec.custom_gpu >= 1
            )
            replicas = self.session.scalars(stmt).fetchall()
            self._update_gpu_specs(gpu_specs, vgpu_specs, replicas)

        # 查询任务下所有gpu和vgpu数据
        if trains := active_resources.get("tn"):
            train_uuids = [tr.uuid for tr in trains]
            stmt = select(TrainReplicaSpec).where(
                TrainReplicaSpec.train_uuid.in_(train_uuids),
                TrainReplicaSpec.custom_gpu >= 1
            )
            replicas = self.session.scalars(stmt).fetchall()
            self._update_gpu_specs(gpu_specs, vgpu_specs, replicas)
        # 查询专属节点所有gpu和vgpu
        if resource_nodes := active_resources.get("rg"):
            node_uuids = [rb.sku_id for rb in resource_nodes]
            user_ids = [rb.user_id for rb in resource_nodes]
            stmt = select(ResourceNode).where(
                ResourceNode.sku_id.in_(node_uuids),
                ResourceNode.gpu >= 1,
                ResourceNode.status == "active",ResourceNode.user_id.in_(user_ids)
            )
            replicas = self.session.scalars(stmt).fetchall()
            for replica in replicas:
                logger.info(f"{replica.sku_id},{len(replicas)}")
                production = get_gpu_referred_sku(replica.sku_id)
                if not production:
                    continue
                gpu_type = production.aipods_type.value
                gpu_memory = production.gpu_memory.name.rstrip("G")
                gpu_model = f"{production.gpu_model.attr_value} {gpu_memory}"
                if production.gpu_memory:
                    if gpu_type == 'vGPU':
                        vgpu_specs[gpu_model] = vgpu_specs.get(gpu_model, 0) + int(production.gpu_count.attr_value)
                    elif gpu_type == 'only_gpu':
                        gpu_specs[gpu_model] = gpu_specs.get(gpu_model, 0) + int(production.gpu_count.attr_value)
        return gpu_specs, vgpu_specs

    def _update_gpu_specs(self, gpu_specs: dict[str, int], vgpu_specs: dict[str, int], replicas: list) -> None:
        for replica in replicas:
            logger.info(f"当前 gpu xinghao {replica}")
            if replica.custom_aipods_type == "only_gpu":
                production = get_gpu_referred_sku(replica.specs)
                if not production:
                    continue
                if production.gpu_memory:
                    gpu_memory = production.gpu_memory.name.rstrip("G")
                    gpu_model = f"{production.gpu_model.attr_value} {gpu_memory}"
                    custom_gpu = replica.custom_gpu
                    replicas_num = replica.replicas
                    total_num = custom_gpu * replicas_num
                    logger.info(f"当前GPU数量为{custom_gpu}, {gpu_model}, {replicas_num}")
                    gpu_specs[gpu_model] = gpu_specs.get(gpu_model, 0) + total_num
            elif replica.custom_aipods_type == "vGPU" and replica.rg_id is None and replica.specs and replica.specs.startswith("sku_"):
                production = get_gpu_referred_sku(replica.specs)
                if not production:
                    continue
                if production.gpu_memory:
                    gpu_memory = production.gpu_memory.name.rstrip("G")
                    vgpu_model = f"{production.gpu_model.attr_value} {gpu_memory}"
                    custom_gpu = replica.custom_gpu
                    replicas_num = replica.replicas
                    total_num = custom_gpu * replicas_num
                    logger.info(f"当前VGPU数量为{custom_gpu}, {vgpu_model}, {total_num}")
                    vgpu_specs[vgpu_model] = vgpu_specs.get(vgpu_model, 0) + total_num

    def _check_basic_quotas(self, user: UserInfo, type: str, num: int, active_resources: dict) -> bool:
        quota_limits = {
            "nb": ("containers_number", len(active_resources["nb"])),
            "tn": ("jobs_number", len(active_resources["tn"])),
            "rg": ("rg_nodes", len(active_resources["rg"]))
        }
        resource_map = {
            "nb": "容器实例",
            "tn": "分布式任务",
            "rg": "专属节点"
        }
        err_type = resource_map.get(type, "实例")
        if type in quota_limits:
            quota_field, current_count = quota_limits[type]
            quota_limit = getattr(user, quota_field, 0)
            logger.info(f"当前数据为{current_count},{num},{quota_limit}")
            # quota_limit 为-1时,不限制配额
            if quota_limit != -1:
                if current_count + num > quota_limit:
                    logger.warning(
                        f"{type.upper()}配额超过限制: 已使用{current_count}+新增{num} > 配额{quota_limit}"
                    )
                    raise NotebookQuotaException(resource=err_type, total=quota_limit,
                                                 used=current_count, required=num)

    def _check_gpu_quotas(self, user: UserInfo, gpu_specs: dict[str, int], vgpu_specs: dict[str, int], num: int,
                          gpu_type: str, gpu_model: str) -> bool:
        """
        检查用户GPU/vGPU配额是否足够
        ARGS:
            user: 用户配额信息
            gpu_specs: 当前使用的GPU规格及数量
            vgpu_specs: 当前使用的vGPU规格及数量
            num: 请求新增的数量
            gpu_type: gpu类型 [gpu, vgpu]
            gpu_model: 资源请求中的gpu型号
        returns:
            True配额足够, False 配额不足
        """
        # 选择当前判断卡类型
        gpu_type = "vgpu" if gpu_type == "vGPU" else "gpu"
        number_attr = f"{gpu_type}_number"
        quotas_attr = f"{gpu_type}_model_quotas"

        if not hasattr(user, number_attr) or not hasattr(user, quotas_attr):
            return True

        gpu_number = getattr(user, number_attr) or 0
        gpu_model_quotas = getattr(user, quotas_attr) or {}

        current_specs = vgpu_specs if gpu_type == "vgpu" else gpu_specs

        logger.info(
            f"GPU配额检查: 类型={gpu_type}, 型号={gpu_model}, "
            f"总数配额={gpu_number}, 型号配额={gpu_model_quotas}, "
            f"当前使用情况={current_specs}, 新增数量={num}"
        )
        resource_map = {
            "gpu": "GPU资源",
            "vgpu": "共享GPU资源",
        }
        err_type = resource_map.get(gpu_type, "GPU资源")
        # 情况1：检查总数限制
        if gpu_number != -1:
            total_used = sum(
                count for spec, count in current_specs.items()
                if spec in gpu_model_quotas
            )

            logger.info(
                f"{gpu_type.upper()}总数使用情况: 已用={total_used}, 新增={num}, 配额={gpu_number}"
            )

            if total_used + num > gpu_number:
                logger.warning(
                    f"{gpu_type.upper()}总数配额不足: 已用{total_used} + 新增{num} > 配额{gpu_number}"
                )
                raise NotebookQuotaException(resource=gpu_type.upper(), total=gpu_number,
                                             used=total_used, required=num)
        else:  # 情况2：检查每种型号的单独限制
            if gpu_model in gpu_model_quotas:
                current_used = current_specs.get(gpu_model, 0)
                quota_limit = gpu_model_quotas[gpu_model]
                quota_value = quota_limit[0] if isinstance(quota_limit, list) else quota_limit
                if quota_value > -1:
                    if current_used + num > quota_value:
                        logger.warning(
                            f"{gpu_type.upper()}型号配额不足: {gpu_model} "
                            f"已用{current_used} + 新增{num} > 配额{quota_value}"
                        )
                        raise NotebookQuotaException(resource=err_type, total=quota_value,
                                                     used=current_used, required=num)

    def _merge_maas_resource_usage(self, specs: dict[str, int], maas_specs: dict[str, int]) -> None:
        """
        合并 maas 的 GPU/VGPU 使用量
        """
        if not specs and maas_specs:
            specs.update(maas_specs)
            return

        for key, original_value in specs.items():
            maas_value = maas_specs.get(key, 0)
            if isinstance(original_value, list) and len(original_value) == 2:
                original_value[0] += maas_value
            else:
                specs[key] += maas_value

    # get user resource num
    def get_user_resource_num(self, user_id: str) -> List[int]:
        active_resources = {
            "nb": self.__get_active_notebook(user_id),
            "tn": self.__get_active_train(user_id),
            "rg": self.__get_active_resource_node(user_id)
        }

        # 计算GPU/VGPU使用情况
        gpu_specs, vgpu_specs = self._get_gpu_specs(active_resources)
        gpu_count = sum(gpu_specs.values())
        vgpu_count = sum(vgpu_specs.values())

        # 初始化用户数据
        used_quota = {
            "docker_used": 0,
            "dir_number": 0,
            "stor_used": 0
        }

        user = QingcloudUser(**get_qingcloud_user_info_by_user_id(user_id), user_from=UserFrom.CONSOLE)
        docker_used = QingcloudDockerApiClient(user).get_repo_quota()
        dir_number, stor_used = QingcloudGpfsClient(user).get_filesets_quota()

        used_quota.update({
            "dir_number": dir_number,
            "stor_used": stor_used,
            "docker_used": docker_used[0],
            "project_used": docker_used[1]
        })

        logger.info(
            f"用户资源使用统计 - 用户ID: {user_id}\n"
            f"Notebook数量: {len(active_resources.get('nb'))}, "
            f"训练任务数量: {len(active_resources.get('tn'))}, "
            f"资源组数量: {len(active_resources.get('rg'))}\n"
            f"GPU总数: {gpu_count} (明细: {gpu_specs}), "
            f"VGPU总数: {vgpu_count} (明细: {vgpu_specs})\n"
            f"存储与镜像用量: {used_quota}"
        )
        return [len(active_resources.get("nb")),
                len(active_resources.get("tn")),
                len(active_resources.get("rg")),
                gpu_count,
                vgpu_count,
                gpu_specs,
                vgpu_specs,
                used_quota]

    def get_user_by_namespaces(self, namespaces: List[str]) -> List[dict]:
        stmt = select(UserInfo).where(UserInfo.namespace.in_(namespaces))
        users: ScalarResult[UserInfo] = self.session.scalars(stmt)
        users_id = [x.user_id for x in users.fetchall()]
        users_info_resp = describe_users(users_id)
        return users_info_resp

    def get_project_category(self):
        stmt = select(ProjectCategory).where(
            or_(ProjectCategory.owner == self.user.user_id, ProjectCategory.owner == self.user.root_user_id))
        project_category: ScalarResult[ProjectCategory] = self.session.scalars(stmt)
        return project_category.fetchall()

    def get_project_category_key_by_name(self, name: str) -> str:
        stmt = select(ProjectCategory.key) \
            .where(ProjectCategory.name == name,
                   or_(ProjectCategory.owner == self.user.user_id, ProjectCategory.owner == self.user.root_user_id))
        project_category: ScalarResult[ProjectCategory] = self.session.scalars(stmt)
        return project_category.first() or "other"

    def delete_project_category(self, project_category_id: int):
        project_category = self.session.get(ProjectCategory, project_category_id)
        if not self.user.is_super_user() and project_category.owner != self.user.user_id:
            raise PermissionDeniedException("project_category_id")
        self.session.delete(project_category)

    def create_project_category(self, body: ProjectCategoryBase):
        if self.user.is_root_user():
            raise PermissionDeniedException("root_user")
        # 获取是否存在同名的
        stmt = select(ProjectCategory).where(ProjectCategory.name == body.name,
                                             ProjectCategory.owner == self.user.user_id)
        project_category: ScalarResult[ProjectCategory] = self.session.scalars(stmt)
        if project_category.first():
            logger.info(f"project category {body.name} already exists.")
            return
        project_category_creator = ProjectCategory(**body.model_dump(), owner=self.user.user_id)
        self.session.add(project_category_creator)

    def get_team_quota(self, user_ids: list[str]):
        """ 查询团队子账号配额及相关信息 """
        if not user_ids:
            return []
        statement = select(UserInfo).where(UserInfo.user_id.in_(user_ids))
        users = self.session.scalars(statement).fetchall()
        user_quota_dict: dict[str, UserInfo] = {user.user_id: user for user in users}
        exist_users = user_quota_dict.keys()
        if exist_users:
            maas_usage_dict = MaasClient().get_maas_server_num(exist_users) or {}
            rg_dict = {}
            sql = ("select t1.rg_id, t1.name, t.share_user_id from resource_group_share t left join resource_group "
                   "t1 on t.rg_id = t1.rg_id where t1.status = 'active' and t.share_user_id in :ids")
            db_ret = self.session.execute(text(sql), {"ids": tuple(exist_users)}).fetchall()
            for item in db_ret:
                rg_id, name, user_id = item[0], item[1], item[2]
                if user_id not in rg_dict:
                    rg_dict[user_id] = []
                rg_dict[user_id].append({'id': rg_id, 'name': name})
        else:
            maas_usage_dict = {}
            rg_dict = {}
        ret_users = []
        for user_id in user_ids:
            _user = user_quota_dict.get(user_id, UserInfo())
            user = {
                "user_id": user_id,
                "quota_containers": _user.containers_number,
                "quota_jobs": _user.jobs_number,
                "quota_resource_nodes": _user.rg_nodes,
                "quota_maas_server": _user.maas_server,
                "priority": _user.priority,
                "dir_number": _user.dir_number,
                "capacity": _user.capacity,
            }
            if user_id in exist_users:
                resource = self.get_user_resource_num(user_id)
                user.update({"containers": resource[0], "jobs": resource[1], "resource_nodes": resource[2],
                             "maas_server": maas_usage_dict.get(user_id, 0), "rgs": rg_dict.get(user_id, [])})
            else:
                user.update({"containers": 0, "jobs": 0, "resource_nodes": 0, "maas_server": 0, "rgs": []})
            ret_users.append(user)
        return ret_users

    def reconcile_quota_numbers(
            self,
            number: int,
            model_quotas: dict,
            current_number: int,
            current_model_quotas: dict
    ) -> tuple[int, dict]:
        """
        处理 GPU 或 vGPU 配额变化逻辑，返回调整后的 (number, model_quotas)
        数量与型号同时只有一个条件生效,数量或者型号
        """
        number_changed = number != current_number
        model_quotas_changed = model_quotas != current_model_quotas

        if number_changed and number >= 0:
            if isinstance(current_model_quotas, dict):
                model_quotas = {k: -1 for k in current_model_quotas.keys()}
            else:
                model_quotas = {}
        elif model_quotas_changed and isinstance(model_quotas, dict):
            number = -1
        logger.info(f"modify the processed data {number}, {model_quotas}")
        return number, model_quotas

    def get_team_quota_detail(self, user_ids: list[str], resource_type: str):
        """ 查询团队子账号配额及相关信息，返回格式: [{user_id, resource_type, resource_count}] """
        if not user_ids:
            return []

        statement = select(UserInfo).where(UserInfo.user_id.in_(user_ids))
        users = self.session.scalars(statement).fetchall()
        user_quota_dict = {user.user_id: user for user in users}
        exist_users = user_quota_dict.keys()

        rg_dict = {}
        if exist_users and resource_type == "rgs":
            sql = (
                "SELECT t1.rg_id, t1.name, t.share_user_id "
                "FROM resource_group_share t "
                "LEFT JOIN resource_group t1 ON t.rg_id = t1.rg_id "
                "WHERE t1.status = 'active' AND t.share_user_id IN :ids"
            )
            db_ret = self.session.execute(text(sql), {"ids": tuple(exist_users)}).fetchall()
            for rg_id, name, user_id in db_ret:
                rg_dict.setdefault(user_id, []).append({'id': rg_id, 'name': name})

        ret_users = []
        for user_id in user_ids:
            _user = user_quota_dict.get(user_id, UserInfo())

            if user_id not in exist_users:
                continue

            maas_server_num, mass_gpu_models, mass_vgpu_models = 0, {}, {}
            if resource_type == "mass":
                maas_usage = MaasClient().get_maas_server_num(user_id) or {}
                logger.info(f"当前返回mass服务接口{maas_usage}")
                maas_data = maas_usage.get(user_id, {})
                maas_server_num = maas_data.get('inference_service_count', 0)
                mass_gpu_models = maas_data.get('gpu', {})
                mass_vgpu_models = maas_data.get('vgpu', {})

            resource = self.get_user_resource_num(user_id)

            if len(resource) <= 7:
                continue

            docker_used, dir_used, stor_used, project_used = 0, 0, 0, 0
            if resource:
                if 'docker_used' in resource[7]:
                    docker_used = resource[7]['docker_used']
                if 'dir_number' in resource[7]:
                    dir_used = resource[7]['dir_number']
                if 'stor_used' in resource[7]:
                    stor_used = resource[7]['stor_used']
                if 'project_used' in resource[7]:
                    project_used = resource[7]['project_used']

            # 处理 GPU/VGPU 用量
            def calculate_device_usage(model_quotas, usage_resource, mass_data):
                total = 0
                for key in model_quotas:
                    original = model_quotas[key]
                    res_val = usage_resource.get(key, 0)
                    mass_val = mass_data.get(key, 0)
                    used = res_val + mass_val
                    if isinstance(original, list) and len(original) == 2:
                        original[1] = used
                    else:
                        model_quotas[key] = [original, used]
                    total += used
                return total

            # resource_type 映射处理
            mapping = {
                "containers": resource[0],
                "jobs": resource[1],
                "resource_nodes": resource[2],
                "mass": maas_server_num,
                "rgs": rg_dict.get(user_id, []),
                "gpu": resource[3],
                "vgpu": resource[4],
                "stor_number": dir_used,
                "stor_quota": stor_used,
                "project_quota": docker_used,
                "project_number": project_used
            }
            resource_count = mapping.get(resource_type, 0)
            logger.info(f"当前请求资源{resource_type}, {resource_count}, {mapping}")
            if resource_count:
                ret_users.append({
                    "user_id": user_id,
                    "resource_type": resource_type,
                    "resource_count": resource_count
                })

        return ret_users

    def check_start_quota(self, notebook_uuid: List, sku_id: str, user_id: str):
        # 校验关机实例是否为CPU,是则跳过校验
        stmt = select(Notebook).where(
            (Notebook.uuid.in_(notebook_uuid)))
        notebook_replicas = self.session.scalars(stmt).fetchall()
        for nb_replica in notebook_replicas:
            stmt = select(NotebookReplicaSpec).where(NotebookReplicaSpec.notebook_uuid == nb_replica.uuid)
            replica_replicas = self.session.scalars(stmt).fetchall()
            for gpu_replica in replica_replicas:
                gpu_count = gpu_replica.custom_gpu
                custom_aipods_type = gpu_replica.custom_aipods_type
                production = get_gpu_referred_sku(sku_id)
                if not production:
                    continue
                gpu_type = production.aipods_type.value
                data_number = int(getattr(production.gpu_count, 'attr_value', 0))
                logger.info(f"当前数据为{data_number}")
                if data_number and (data_number > gpu_count or custom_aipods_type != gpu_type):
                    self.quota_verification(user_id, "nb", 1, sku_id)



class SharedNamespace():

    def __init__(self, share_from: str, share_to: str, permission: SharedPermissionEnum, **kwargs):
        self.share_from = share_from
        self.share_to = share_to
        self.share_permission = permission

    @property
    def rolebinding_name(self):
        return f"user-{self.namespace_share_to}-clusterrole-{self.share_permission}"

    @property
    def authorization_policy_name(self):
        return f"user-{self.namespace_share_to}-clusterrole-{self.share_permission}"

    @property
    def namespace_share_from(self):
        return self.share_from.lower()

    @property
    def namespace_share_to(self):
        return self.share_to.lower()

    @property
    def rolebind_json(self):
        return {
            "apiVersion": "rbac.authorization.k8s.io/v1",
            "kind": "RoleBinding",
            "metadata": {
                "annotations": {
                    "role": self.share_permission,
                    "user": self.share_to
                },
                "name": self.rolebinding_name,
                "namespace": self.namespace_share_from,
            },
            "roleRef": {
                "apiGroup": "rbac.authorization.k8s.io",
                "kind": "ClusterRole",
                "name": f"kubeflow-{self.share_permission}"
            },
            "subjects": [
                {
                    "apiGroup": "rbac.authorization.k8s.io",
                    "kind": "User",
                    "name": self.share_to
                }
            ]
        }

    @property
    def authorization_policy_json(self):
        return {
            "apiVersion": "security.istio.io/v1",
            "kind": "AuthorizationPolicy",
            "metadata": {
                "annotations": {
                    "role": self.share_permission,
                    "user": self.share_to
                },
                "name": self.authorization_policy_name,
                "namespace": self.namespace_share_from
            },
            "spec": {
                "rules": [
                    {
                        "from": [
                            {
                                "source": {
                                    "principals": [
                                        "cluster.local/ns/istio-system/sa/istio-ingressgateway-service-account",
                                        "cluster.local/ns/kubeflow/sa/ml-pipeline-ui"
                                    ]
                                }
                            }
                        ],
                        "when": [
                            {
                                "key": "request.headers[kubeflow-userid]",
                                "values": [
                                    self.share_to
                                ]
                            }
                        ]
                    }
                ]
            }
        }

    def get_namespace(self):
        return Namespace.get(self.namespace_share_from)

    def get_rolebinding(self) -> Optional[RoleBinding]:
        try:
            return RoleBinding.get(self.rolebinding_name, namespace=self.namespace_share_from)
        except NotFoundError as e:
            logger.info(f"RoleBinding {self.rolebinding_name} not found.")
            return None

    def get_authorization_policy(self) -> Optional[AuthorizationPolicy]:
        try:
            return AuthorizationPolicy.get(self.authorization_policy_name, namespace=self.namespace_share_from)
        except NotFoundError as e:
            logger.info(f"AuthorizationPolicy {self.authorization_policy_name} not found.")
            return None

    def create(self) -> bool:
        changed = False
        logger.info(
            f"share namespace {self.namespace_share_from} to {self.namespace_share_to} with permission {self.share_permission}")
        namespace = self.get_namespace()

        if self.get_rolebinding() is None:
            logger.info(f"craete rolebinding {self.rolebinding_name} in namespace {self.namespace_share_from}")
            rolebinding = RoleBinding(self.rolebind_json)
            rolebinding.create()
            rolebinding.set_owner(namespace)
            changed = True

        if self.get_authorization_policy() is None:
            logger.info(
                f"craete authorization_policy {self.authorization_policy_name} in namespace {self.namespace_share_from}")
            authorization_policy = AuthorizationPolicy(self.authorization_policy_json)
            authorization_policy.create()
            authorization_policy.set_owner(namespace)
            changed = True

        logger.info(f"share namespace {self.namespace_share_from} to {self.namespace_share_to} successfully.")
        return changed


def get_gpu_referred_sku(sku_id: str):
    try:
        production = ProductCenterResource(sku_id)
    except ResourceNotFoundException as e:
        logger.warning(f'sku:{sku_id} not found in product center')
        logger.warning(e)
        return

    if not production.aipods_type or not production.gpu_memory or production.gpu_model:
        logger.warning(f'sku:{sku_id} is not a valid gpu sku')
        return

    return production

