from datetime import datetime
from typing import Any

from sqlmodel import select, or_
from sqlmodel import Session

from app import logger
from app.core.middlewares.auth.qingcloud_auth import QingcloudUser
from app.core.utils import generate_random_string
from app.models.public_key import PublicKey<PERSON>reate, PublicKey



class PublicKeysCrud:
    """
    Train CRUD
    """

    def __init__(self, session: Session):
        self.session: Session = session

    def generate_rg_id(self, exclude_ids=None):
        while True:
            pk_id = "pk-" + generate_random_string()
            logger.info("get pk_id [%s]", pk_id)
            if exclude_ids and pk_id in exclude_ids:
                continue
            return pk_id

    def create_pulic_key(self, public_key_create: PublicKeyCreate, user: QingcloudUser) -> Any:
        public_key = PublicKey(**public_key_create.model_dump())
        public_key.user_id = user.user_id
        public_key.root_user_id = user.user_id
        public_key.pk_id = self.generate_rg_id()
        public_key.create_time=datetime.now()
        self.session.add(public_key)
        self.session.commit()
        self.session.refresh(public_key)
        return public_key

    def delete_rg(self, pk_id: str, user: QingcloudUser) -> Any:

        results = self.session.exec(select(
            PublicKey).where(PublicKey.pk_id == pk_id,
                                 PublicKey.user_id == user.user_id))
        rg = results.one()
        self.session.delete(rg)
        self.session.commit()
        return True

    def get_pk_by_user(self, user_id: str, order_by="create_time", reverse=False, search_word=None):

        sql = select(PublicKey).where(PublicKey.user_id == user_id)
        if search_word:
            sql = sql.where(or_(PublicKey.name.like(f"%{search_word}%"),
                                PublicKey.pk_id.like(f"%{search_word}%")))
        if reverse:
            order_by = getattr(PublicKey, order_by).desc()
        else:
            order_by = getattr(PublicKey, order_by)
        sql = sql.order_by(order_by)
        result = self.session.exec(sql)
        public_key = result.fetchall()
        logger.info("get public_key [%s]", public_key)
        return public_key