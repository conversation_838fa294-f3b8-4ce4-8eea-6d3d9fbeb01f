import contextvars
from typing import Any, Dict, List, Union

import yaml
from pydantic import ConfigDict
from pydantic.dataclasses import dataclass
from pydantic_settings import BaseSettings


@dataclass
class LocalPathStorageMount:
    HOST_PATH: str
    MOUNT_PATH: str
    PERMISSION: str = "rw"


class BaseConfig(BaseSettings):
    # Base
    api_v1_prefix: str = "/aicp"
    api_v1_prefix_qai: str = "/qai/aicp"
    api_kapi_prefix: str = "/kapis/aicp.kubesphere.io/v1/aicp"
    debug: bool = False
    project_name: str = "AICP"
    version: str = "2.5.0"
    description: str = "aicp - AI Cloud Platform"
    exclude_url: list[str] = ["/healthz", "/aicp-docs", "/aicp-openapi.json"]
    allowed_options_for_exclude_url: List[str] = ["GET", "OPTIONS", "POST"]
    export_swagger_mode: bool = False

    # billing
    billing_enable: bool = False

    cluster_name: str = "host"


class DataBaseConfig(BaseSettings):
    # Database
    DB_USERNAME: str = 'aicp'
    DB_PASSWORD: str = 'Hpcadmin123'
    DB_HOST: str = "pg-readwrite.aicp-storage.svc"
    DB_PORT: int = 5432
    DB_DATABASE: str = "aicp"
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_POOL_RECYCLE: int = 3600
    DB_POOL_PRE_PING: bool = True
    ECHO: bool = False

    @property
    def DB_CONNECTION_STR(self):
        return f"postgresql+psycopg2://{self.DB_USERNAME}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}"

    @property
    def DB_ASYNC_CONNECTION_STR(self):
        return f"postgresql+asyncpg://{self.DB_USERNAME}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}"


class RedisConfig(BaseSettings):
    REDIS_HOST: str = "redis.pitrix.svc"
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str = ""
    REDIS_PREFIX: str = "operation:"


class OpensearchConfig(BaseSettings):
    OPENSEARCH_HOST: str = "https://opensearch-cluster-master.kubesphere-logging-system.svc:9200"
    OPENSEARCH_USER: str = "admin"
    OPENSEARCH_PASSWORD: str = "admin"


class QingcloudConfig(BaseSettings):
    # qingcloud
    qingcloud_access_key_id: str
    qingcloud_secret_access_key: str
    qingcloud_zone: str
    qingcloud_zone_zh: str = ""
    qingcloud_host: str
    qingcloud_port: int
    qingcloud_protocol: str
    default_console_id: str
    default_regin_id: str

    @property
    def qingcloud(self) -> Dict[str, Any]:
        return {
            "qingcloud_access_key_id": self.qingcloud_access_key_id,
            "qingcloud_secret_access_key": self.qingcloud_secret_access_key,
            "qingcloud_default_zone": self.qingcloud_zone,
            "qingcloud_host": self.qingcloud_host,
            "qingcloud_port": self.qingcloud_port,
            "qingcloud_protocol": self.qingcloud_protocol,
            "default_console_id": self.default_console_id,
            "default_regin_id": self.default_regin_id
        }


class GPFSConfig(BaseSettings):
    # GPFS
    QINGCLOUD_GPFS_ENABLED: bool = False
    QINGCLOUD_GPFS_DEBUG: bool = False
    QINGCLOUD_GPFS_BACKEND: str = "HOSTPATH"  # GPFS / SUGON / HOSTPATH
    QINGCLOUD_GPFS_SERVER: str = "http://istio-ingressgateway.istio-system.svc.cluster.local/"
    QINGCLOUD_GPSE_ZONE: str = None
    QINGCLOUD_GPSE_FILESYSTEM: str = None
    QINGCLOUD_GPSE_VOLUME_HANDLE: str = None
    # if you use hostpath backend, the following config should be set
    QINGCLOUD_GPSE_HOSTPATH_BACKEND_HOSTPATH: str = "/public"
    # if you use sugon backend, the following config should be set, it used posix protocol
    QINGCLOUD_GPFS_SUGON_BACKEND_STORAGE_CLASS_NAME: str = "parastorposix-csi"
    QINGCLOUD_GPFS_SUGON_BACKEND_PROTOCOL: str = "posix"
    QINGCLOUD_GPFS_SUGON_BACKEND_STORAGE_POOL: str = "data_storage_pool"
    QINGCLOUD_GPFS_SUGON_BACKEND_CLUSTER_NAME: str = "zw_dx_01_03_b1"
    QINGCLOUD_GPFS_SUGON_BACKEND_FILESYSTEM_ID: str = "fs1"
    QINGCLOUD_GPFS_SUGON_BACKEND_SECRET_NAME: str = "sugon-backend"
    QINGCLOUD_GPFS_SUGON_BACKEND_SECRET_NAMESPACE: str = "kube-system"


class IbTypes:
    IB = "IB"
    ROCE = "RoCE"
    NO = ""


class NotebookAndTrainRelationConfig(BaseSettings):
    # SSH HOST
    SSH_HOST: str = ""

    # NOTEBOOK_HOST
    NOTEBOOK_HOST: str
    NOTEBOOK_INIT_CONTAINER_IMAGE: str = "dockerhub.aicp.local/aicp-common/aicp/notebook-init-container:latest"
    NOTEBOOK_INIT_CONTAINER_SIZE_LIMIT: str = "500Mi"
    TENSORBOARD_URL: str
    NOTEBOOK_INGRESS_BANDWIDTH: str = "1000M"
    NOTEBOOK_EGRESS_BANDWIDTH: str = "1000M"
    # NOTEBOOK_PROXY
    NOTEBOOK_ENABLE_PROXY: bool = True

    # notebook retention time (days), -1 means never delete
    NOTEBOOK_RETENTION_TIME: int = 15
    delete_node_delay_time: int = 3600

    # ENV
    AICP_PLATFORM: str = "coreshub"
    ENV_VARIABLE_PREFIX_NOTEBOOK: str = ""
    ENV_VARIABLE_PREFIX_TRAIN: str = ""
    # see https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/docker-specialized.html#driver-capabilities
    NVIDIA_DRIVER_CAPABILITIES: str = ""

    # registry
    DOCKER_REGISTRY: str = "j1-dockerhub.qingcloud.com"
    DOCKER_ADMIN_USER: str = "admin"
    DOCKER_ADMIN_PASSWORD: str = "zhu88jie"
    DOCKER_REGISTRY_PROTOCOL: str = "https"
    # IMAGE_SAVE_TIMEOUT Unit: minute
    IMAGE_SAVE_TIMEOUT: int = 60
    IMAGE_DUMP_PATH: str = "/share/public/image_builders"
    IMAGE_DUMP_DELETE_DAYS: int = 7

    VOLCANO_ENABLE: bool = False
    VOLCANO_CONFIGMAP_NAME: str = "volcano-scheduler-configmap"
    VOLCANO_NAMESPACE: str = "volcano-system"
    ft_image: str = "aicp/aicp/llama-factory:latest"

    # ib hostdev
    # if IB_TYPE = RoCE,
    # ROCE should be set as [hostdev1, hostdev2, ... ]
    # ROCE_NETS should be set as [default/roce-hostdevice-net1, default/roce-hostdevice-net2, ... ]
    # see https://cwiki.yunify.com/pages/viewpage.action?pageId=224901517
    IB_HOSTDEV_NUMBER: int = 0
    IB_TYPE: str = IbTypes.IB  # IB or RoCE or ""(BLANK)
    ROCE: List[str] = []  # list of roce device
    ROCE_NETS: List[str] = []  # list of roce net

    # tensorboard logs path
    TRAIN_CODE_ENABLE: bool = True
    TRAIN_CODE_MOUNT_PATH: str = "/root/code"
    TENSORBOARD_ENABLE: bool = True
    TENSORBOARD_MOUNT_PATH: str = "/tensorboard_logs"

    DELETE_IMMEDIATELY_WHEN_UNSCHEDULABLE: bool = False

    USE_NETWORK_POLICY: bool = True


class TmpConfig(BaseSettings):
    # MINIO
    TMP_DIR: str = "/code/migration/versions/tmp"


class VolumeRelationConfig(BaseSettings):
    # LOCAL STORAGE CLASS
    LOCAL_STORAGE_ENABLE: bool = False
    LOCAL_STORAGE_CLASS: str = "openebs-zfspv"

    PUBLIC_NFS_SERVER: str = ''
    PUBLIC_NFS_PATH: str = "/mnt/sdb/model"
    PUBLIC_NFS_IN_POD_PATH: str = "/root/public"
    # LOCAL PATH STORAGE CLASS
    LOCAL_PATH_STORAGE_MOUNT_PATHS: Union[List[LocalPathStorageMount], List[str], Dict[str, str]] = []

    MODEL_MANAGE_SERVER: str = "http://model-manage-server.maas-system.svc.cluster.local/maas/admin/billing/action"
    MODEL_MOUNT_ENABLE: bool = True
    HOST_MODEL_MOUNT_PATH: str = '/share/maasfile/model/public'
    HOST_MODEL_NAME_MOUNT_PATH: str = '/root/public/model'
    POD_MODEL_NAME_MOUNT_PATH: str = '/share/maasfile/model/public'
    SECRET_PATH: str = "/share/secret"
    # 是否开启挂载自定义pvc功能, 默认关闭, 开启的话MANUAL_PVC_FILTER下的pvc会被过滤掉不在前端展示
    MANUAL_PVC: bool = False
    MANUAL_PVC_FILTER: str = "^(openebs-zfs|local-hostpath|aicp-oss-pvc|public-nfs-pvc|qingcloud-gpfs-pvc|longhorn-worker).*"

    # 资源剩余量检查
    ENABLE_SALABLE_CHECK: bool = True
    ENABLE_SALABLE_CHECK_FOR_NOTEBOOK: bool = True
    ENABLE_SALABLE_CHECK_FOR_TRAIN: bool = True
    ENABLE_SALABLE_CHECK_FOR_INFERENCE: bool = True
    ENABLE_SALABLE_CHECK_FOR_RESOURCE_GROUP: bool = True
    # see https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/latest/docker-specialized.html#driver-capabilities
    NVIDIA_DRIVER_CAPABILITIES: str = ""


class GpuConfig(BaseSettings):
    gpu_annotations_key: str = "qingcloud/node-nvidia-register"
    VGPU_TAG: List[str] = ["vGPU", "adapter", "vDCU"]

    # VGPU 的优先级高于 GPU 节点
    GPU_MAP: dict = {
        "VGPU": [
            {
                "match": "^It's not impassable, only ready for vgpu$",
                "label": "nvidia.com/gpu.product",
                "resource": "qingcloud.nvidia.com/vgpu"
            },
            {
                "match": "^It's not impassable, only ready for vnpu$",
                "label": "servertype",
                "resource": "qingcloud.adapter.com/adapter"
            },
            {
                "match": "^It's not impassable, only ready for vdcu$",
                "label": "feature.node.kubernetes.io/gpu-model",
                "resource": "qingcloud.hygon.com/dcunum"
            }
        ],
        "NVIDIA": [
            {
                "match": "^(NVIDIA|Tesla).*$",
                "label": "nvidia.com/gpu.product",
                "resource": "nvidia.com/gpu",
                "virtual": "vGPU"
            }
        ],
        "HEXAFLAKE": [
            {
                "match": "^C10.*$",
                "label": "hexaflake.com/aigpu.product",
                "resource": "hexaflake/gpu"
            }
        ],
        "HYGON": [
            {
                "match": "^Hygon.*$",
                "label": "feature.node.kubernetes.io/gpu-model",
                "resource": "hygon.com/dcu",
                "virtual": "vDCU"
            }
        ],
        "ASCEND": [
            {
                "match": "^Ascend910.*$",
                "label": "servertype",
                "resource": "huawei.com/Ascend910",
                "virtual": "adapter"
            },
            {
                "match": "^Ascend310P.*$",
                "label": "servertype",
                "resource": "huawei.com/Ascend310P",
                "virtual": "adapter"
            }
        ]
    }


class QuotaConfig(BaseSettings):
    # 资源剩余量检查
    ENABLE_SALABLE_CHECK: bool = True
    ENABLE_SALABLE_CHECK_FOR_NOTEBOOK: bool = True
    ENABLE_SALABLE_CHECK_FOR_TRAIN: bool = True
    ENABLE_SALABLE_CHECK_FOR_INFERENCE: bool = True
    ENABLE_SALABLE_CHECK_FOR_RESOURCE_GROUP: bool = True
    # Initialize quota
    POD_NUMBER: int = 10
    JOB_NUMBER: int = 10
    RG_NODES: int = 10
    MAAS_SERVER: int = 10
    GPU_NUMBER: int = 80
    # set the max number of nodes per notebook, default is 10, infinite is <=0
    NODE_PORT_PER_NOTEBOOK: int = 10
    # how long to keep the notebook local storage pvc when notebook is deleted
    PVC_RETENTION_DAYS: int = 5
    PROFILE_RETENTION_DAYS: int = 30
    DIR_NUMBER: int = 1
    DOCKER_QUOTA: int = 100
    PROJECT_NUMBER: int = 10
    CAPACITY: int = 2048



class ThirdApiServerConfig(BaseSettings):
    # DOCKER API SERVER
    QINGCLOUD_DOCKER_API_SERVER: str = "http://istio-ingressgateway.istio-system.svc.cluster.local/"

    # push server 所有zone都应该公用这一个push server
    AI_CLOUD_PUSH_SERVER: str = "http://push-server-service.aicp-system:32149/push/ms"

    # Message
    IAAS_MESSAGE_ENABLE: bool = True

    # Maas server
    MAAS_SERVER_SVC: str = "model-manage-server.maas-system.svc.cluster.local"

    # Global服务地址
    GLOBAL_SERVER_URL: str = "https://hpc-ai.qingcloud.com"


class MockUserConfig(BaseSettings):
    MOCK_USER_ID: str = ""
    MOCK_USER_AK: str = ""
    MOCK_USER_SK: str = ""

    @property
    def mock_user(self) -> Dict[str, Any]:
        return {
            'user_id': self.MOCK_USER_ID or 'usr-mock-user-id',
            'role': 'global_admin',
            'user_name': self.MOCK_USER_ID or 'usr-mock-user-id',
            "privilege": 10,
            'email': '<EMAIL>',
            'phone': '',
            'root_user_id': self.MOCK_USER_ID or 'usr-mock-user-id',
            'access_key_id': self.MOCK_USER_AK,
            'secret_access_key': self.MOCK_USER_SK
        }


class UFMConfig(BaseSettings):
    # UFM info
    UFM_ENABLE: bool = False
    UFM_ADDRESS: str = "https://"
    UFM_USERNAME: str = "username"
    UFM_PASSWORD: str = "password"


class Settings(
    BaseConfig,
    DataBaseConfig,
    QingcloudConfig,
    GPFSConfig,
    RedisConfig,
    OpensearchConfig,
    NotebookAndTrainRelationConfig,
    TmpConfig,
    VolumeRelationConfig,
    GpuConfig,
    QuotaConfig,
    ThirdApiServerConfig,
    MockUserConfig,
    UFMConfig
):
    DOCKER_OS_DISK: str = "/aicp/dockerRootDir"
    DOCKER_DATA_DISK: str = "/zfspv-pool"

    # support subuser get all trains
    SUPPORT_SUBUSER_GET_ALL_OF_ROOT_USER: bool = False

    # cpu / mem oversold, default is 1.0,
    # For example, the oversold ratio is set to 1.2, and when the user request 4000(msc), the actual request is 4000/1.2 = 3333(msc)
    OVERSOLD_RATIO: float = 1.0
    OVERSOLD_RATIO_MEM: float = 1.0

    model_config = ConfigDict(extra='allow')

    @staticmethod
    def yaml_load(file_path: str):
        """
        Yaml load config.
        """
        with open(file_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    @classmethod
    def from_yaml(cls, file_path: str):
        """
        Yaml to settings.
        """
        data = cls.yaml_load(file_path)
        return cls(**data)


request_id_context = contextvars.ContextVar('request-id')
user_id_context = contextvars.ContextVar('user-id')
coroutine_id = contextvars.ContextVar('coroutine_id')


def get_user_id_context_value(default=None):
    """
    获取 user_id_context 的值
    :param default:
    :return:
    """
    try:
        var = user_id_context.get(default)
        if var is None:
            return default
        if var is Ellipsis:
            return None
        return var
    except Exception as e:
        return default


def get_request_id_context_value(default=None):
    """
    获取 request_id_context 的值
    :param default:
    :return:
    """
    try:
        var = request_id_context.get(default)
        if var is None:
            return default
        if var is Ellipsis:
            return None
        return var
    except Exception as e:
        return default


def get_coroutine_id_value(default=None):
    """
    获取 coroutine_id 的值
    :param default:
    :return:
    """
    try:
        var = coroutine_id.get(default)
        if var is None:
            return default
        if var is Ellipsis:
            return None
        return var
    except Exception as e:
        return default
